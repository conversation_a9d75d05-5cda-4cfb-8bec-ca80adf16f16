import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getTemplateList = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessageTemplate/list',
    params
  })
}

export const getTemplateById = (params) => {
  return request({
    method: 'get',
    url: '/sys/message/sysMessageTemplate/queryById',
    params
  })
}

export const addTemplate = (data) => {
  return request({
    method: 'post',
    url: '/sys/message/sysMessageTemplate/add',
    data
  })
}

export const updateTemplate = (data) => {
  return request({
    method: 'put',
    url: '/sys/message/sysMessageTemplate/edit',
    data
  })
}

export const deleteTemplate = (params) => {
  return request({
    method: 'delete',
    url: '/sys/message/sysMessageTemplate/delete',
    params
  })
}

export const batchDeleteTemplate = (params) => {
  return request({
    method: 'delete',
    url: '/sys/message/sysMessageTemplate/deleteBatch',
    params
  })
}

export const updateStatus = (data) => {
  return request({
    method: 'put',
    url: '/sys/message/sysMessageTemplate/edit',
    data
  })
}

export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/sys/message/sysMessageTemplate/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

export const importExcel = (data, controller) => {
  return advanceUpload('/sys/message/sysMessageTemplate/importExcel', data, controller)
}

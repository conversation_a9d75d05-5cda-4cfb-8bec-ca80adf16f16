<template>
  <a-drawer
    v-model:open="visible"
    class="edit-menu-drawer"
    :title="`字典列表[${dictInfo.dictName}]`"
    placement="right"
    width="800px"
  >
    <div class="flex justify-between my-[24px]">
      <a-form layout="inline" ref="formRef" class="mb-[10px]" autocomplete="off">
        <a-form-item label="名称">
          <s-input
            v-model="params.itemText"
            placeholder="请输入名称"
            @keyup.enter="onTableChange({ current: 1, pageSize: pagination.pageSize })"
          ></s-input>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="onTableChange({ current: 1, pageSize: pagination.pageSize })">查询</a-button>
          <a-button @click="handleReset">重置</a-button>
        </a-form-item>
      </a-form>
      <a-button type="primary" @click="handleAdd">新增</a-button>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'itemColor'">
          <div class="w-[18px] h-[18px] rounded-full" :style="{ backgroundColor: record.itemColor }"></div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-popconfirm title="是否确认删除？" ok-text="确认" cancel-text="取消" @confirm="handleRemove(record)">
            <span class="primary-btn">删除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <a-modal
      v-model:open="editVisible"
      :title="form.id ? '编辑' : '新增'"
      width="500px"
      wrap-class-name="common-modal"
      :confirm-loading="confirmLoading"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '70px' } }" autocomplete="off">
        <a-form-item label="名称" name="itemText">
          <a-input v-model:value="form.itemText" placeholder="请输入名称" :maxlength="10" />
        </a-form-item>
        <a-form-item label="数据值" name="itemValue">
          <a-input v-model:value="form.itemValue" placeholder="请输入数据值" :maxlength="10" />
        </a-form-item>
        <a-form-item label="颜色值" name="itemColor">
          <a-input v-model:value="form.itemColor" placeholder="请输入颜色值，或选择下方快捷选项"></a-input>
          <div class="flex items-center gap-[10px] mt-[10px]">
            <div>快捷选项:</div>
            <div
              v-for="color in colors"
              :key="color"
              class="w-[18px] h-[18px] rounded-full cursor-pointer"
              :style="{ backgroundColor: color }"
              @click="handleSelectColor(color)"
            ></div>
          </div>
        </a-form-item>
        <a-form-item label="描述" name="description">
          <a-textarea v-model:value="form.description" placeholder="请输入字典描述" :maxlength="200" />
        </a-form-item>
        <a-form-item label="排序" name="sortNo">
          <a-input v-model:value="form.sortNo" placeholder="请输入排序" :maxlength="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-drawer>
</template>

<script setup>
import { validateInteger, validateColor } from '@/utils/validate'
import { getDictItemList, addDictItem, editDictItem, deleteDictItem, checkDictItem } from '../apis'
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'

const visible = ref(false)
const dictInfo = reactive({})

const open = (data) => {
  Object.assign(dictInfo, data)
  params.dictId = data.id
  params.itemText = ''
  form.dictId = data.id
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
}

const params = reactive({
  itemText: '',
  dictId: ''
})

const columns = [
  { title: '名称', dataIndex: 'itemText' },
  { title: '数据值', dataIndex: 'itemValue' },
  { title: '颜色值', dataIndex: 'itemColor' },
  { title: '操作', dataIndex: 'action', width: 200 }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(getDictItemList)
const onTableChange = ({ pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo, pageSize, ...params })
}
const handleReset = () => {
  params.itemText = ''
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
}

const form = reactive({
  id: '',
  dictId: '',
  description: '',
  itemText: '',
  itemValue: '',
  itemColor: '',
  sortNo: '1',
  status: '1'
})

const validateItemValue = async (rule, value) => {
  if (!value) return Promise.reject('请输入数据值')
  try {
    await checkDictItem({
      dictId: form.dictId,
      id: form.id || undefined,
      itemValue: value
    })
  } catch (error) {
    return Promise.reject(error.message)
  }
}
const rules = {
  itemText: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  itemValue: [{ required: true, validator: validateItemValue, trigger: 'blur' }],
  itemColor: [{ required: false, validator: validateColor(false, '颜色值'), trigger: 'blur' }],
  sortNo: [{ required: false, validator: validateInteger(false, '排序'), trigger: 'blur' }]
}

const confirmLoading = ref(false)
const formRef = ref()
const handleConfirm = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    form.id ? await editDictItem(form) : await addDictItem(form)
    message.success(form.id ? '编辑成功' : '新增成功')
    form.id ? onTableChange(pagination.value) : onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.description = ''
  form.itemText = ''
  form.itemValue = ''
  form.itemColor = ''
  form.sortNo = '1'
  form.status = '1'
  formRef.value.clearValidate()
  editVisible.value = false
}

const editVisible = ref(false)
const handleAdd = () => {
  editVisible.value = true
}

const handleEdit = (data) => {
  form.id = data.id
  form.description = data.description
  form.itemText = data.itemText
  form.itemColor = data.itemColor
  form.itemValue = data.itemValue
  form.sortNo = data.sortNo
  form.status = data.status
  editVisible.value = true
}

const handleRemove = async (data) => {
  await deleteDictItem({ id: data.id })
  message.success('删除成功')
  let pageNo = pagination.value.current
  if (pageNo > 1 && list.value.length === 1) {
    pageNo--
  }
  onTableChange({ pageNo, pageSize: pagination.value.pageSize })
}

const colors = ['#1d64f0', '#fab700', '#f03a1d', '#6ec21b', '#d7dae0', '#eb2f96', '#7500ea', '#2d46c4']
const handleSelectColor = (color) => {
  form.itemColor = color
}

defineExpose({ open })
</script>

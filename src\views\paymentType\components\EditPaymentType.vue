<template>
  <a-drawer
    v-model:open="visible"
    class="edit-payment-type-drawer common-drawer"
    :title="form.id ? '编辑款项类型' : '新建款项类型'"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form
        :model="form"
        ref="formRef"
        :rules="rules"
        :label-col="{ style: { width: '136px' } }"
        label-align="left"
        autocomplete="off"
      >
        <h2 class="text-[16px] font-bold mb-[20px] w-full">基础信息</h2>
        <a-form-item label="款项类型名称" name="name">
          <a-input v-model:value="form.name" placeholder="请输入款项类型名称" :maxlength="20"></a-input>
        </a-form-item>
        <a-form-item label="款项类型" name="paymentProperties">
          <dict-select v-model="form.paymentProperties" code="CT_BASE_ENUM_PaymentType_PaymentProperties"></dict-select>
        </a-form-item>
        <a-form-item label="物业管理公司" name="manageCompany">
          <dept-tree-select v-model="form.manageCompany" placeholder="请选择物业管理公司"></dept-tree-select>
        </a-form-item>
        <a-form-item label="合同协议类型" name="contractType">
          <dict-select v-model="form.contractType" code="CT_BASE_ENUM_PaymentType_PaymentProperties"></dict-select>
        </a-form-item>
        <a-form-item label="是否周期性缴交" name="periodPayContract">
          <a-select v-model:value="form.periodPayContract">
            <a-select-option value="true">是</a-select-option>
            <a-select-option value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="是否不开票" name="noInvoice">
          <a-select v-model:value="form.noInvoice">
            <a-select-option value="true">是</a-select-option>
            <a-select-option value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="是否纳入计提印花税" name="includeAccrualStamp">
          <a-select v-model:value="form.includeAccrualStamp">
            <a-select-option value="true">是</a-select-option>
            <a-select-option value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="是否押金" name="isDeposit">
          <a-select v-model:value="form.isDeposit">
            <a-select-option value="true">是</a-select-option>
            <a-select-option value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="备注" name="remark" class="!w-full">
          <a-textarea
            v-model:value="form.remark"
            placeholder="请输入备注(选填)"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
        <h2 class="text-[16px] font-bold mb-[20px] w-full mt-[40px]">单据必填字段校验</h2>
        <a-form-item label="应收单" class="!w-full">
          <a-checkbox-group v-model:value="bill" :options="options" />
        </a-form-item>
        <a-form-item label="收付款记录" class="!w-full">
          <a-checkbox-group v-model:value="record" :options="options" />
        </a-form-item>
      </a-form>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { add, edit, detail, queryPaymentTypeReceiveBill, queryPaymentTypePayExplainBook } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.status = result.status
  form.number = result.number
  form.name = result.name
  form.paymentProperties = result.paymentProperties
  form.manageCompany = result.manageCompany
  form.contractType = result.contractType
  form.periodPayContract = result.periodPayContract ? 'true' : result.periodPayContract === false ? 'false' : ''
  form.noInvoice = result.noInvoice ? 'true' : result.noInvoice === false ? 'false' : ''
  form.includeAccrualStamp = result.includeAccrualStamp ? 'true' : result.includeAccrualStamp === false ? 'false' : ''
  form.isDeposit = result.isDeposit ? 'true' : result.isDeposit === false ? 'false' : ''
  form.remark = result.remark
  const data = await Promise.all([queryPaymentTypeReceiveBill({ id }), queryPaymentTypePayExplainBook({ id })])
  form.paymentTypeReceiveBillList = data[0].result
  form.paymentTypePayExplainBookList = data[1].result
  for (const key in checkedObj) {
    if (form.paymentTypePayExplainBookList[0][key]) {
      record.value.push(key)
    }
    if (form.paymentTypeReceiveBillList[0][key]) {
      bill.value.push(key)
    }
  }
  loading.value = false
}

const options = [
  { label: '租赁单元', value: 'leaseUnit' },
  { label: '水电表号', value: 'waterEleTableNum' },
  { label: '合同号', value: 'contractNum' },
  { label: '车位号', value: 'carportNum' },
  { label: '服务处', value: 'serviceCenter' },
  { label: '应收开始日期', value: 'receiveBeginDate' },
  { label: '应收结束日期', value: 'receiveEndDate' },
  { label: '收入归属年月', value: 'incomeBelongYm' }
]
/**
 * 已选字段，这是后端要求的提交格式
 * form.paymentTypePayExplainBookList和form.paymentTypePayExplainBookList的提交格式举例:
 * paymentTypePayExplainBookList: [checkedObj]
 */
const checkedObj = {
  leaseUnit: false,
  waterEleTableNum: false,
  contractNum: false,
  carportNum: false,
  serviceCenter: false,
  receiveBeginDate: false,
  receiveEndDate: false,
  incomeBelongYm: false
}

const form = reactive({
  id: '',
  status: 'ENABLE',
  number: '',
  name: '',
  paymentProperties: '',
  manageCompany: '',
  contractType: '',
  periodPayContract: '',
  noInvoice: '',
  includeAccrualStamp: '',
  isDeposit: '',
  remark: '',
  paymentTypePayExplainBookList: [{ ...checkedObj }],
  paymentTypeReceiveBillList: [{ ...checkedObj }]
})

const bill = ref([]) // 应收单
const record = ref([]) // 收付款记录

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  paymentProperties: [{ required: true, message: '请选择款项类型', trigger: 'change' }],
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    for (const key in checkedObj) {
      form.paymentTypeReceiveBillList[0][key] = bill.value.includes(key)
      form.paymentTypePayExplainBookList[0][key] = record.value.includes(key)
    }
    const params = { ...form }
    params.periodPayContract = form.periodPayContract === 'true'
    params.noInvoice = form.noInvoice === 'true'
    params.includeAccrualStamp = form.includeAccrualStamp === 'true'
    params.isDeposit = form.isDeposit === 'true'
    form.id ? await edit(params) : await add(params)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.status = 'ENABLE'
  form.number = ''
  form.name = ''
  form.paymentProperties = ''
  form.manageCompany = ''
  form.contractType = ''
  form.periodPayContract = ''
  form.noInvoice = ''
  form.includeAccrualStamp = ''
  form.isDeposit = ''
  form.remark = ''
  form.paymentTypePayExplainBookList = [{ ...checkedObj }]
  form.paymentTypeReceiveBillList = [{ ...checkedObj }]
  bill.value = []
  record.value = []
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-payment-type-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 40px;
    .ant-form-item {
      width: calc(50% - 20px);
    }
  }
}
</style>

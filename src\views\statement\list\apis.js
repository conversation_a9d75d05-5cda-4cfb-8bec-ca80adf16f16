import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

export const getPage = (params) => {
  return request({
    method: 'get',
    url: '',
    params
  })
}

// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/receiveBill/queryById?id=${id}`
  })
}
// 详情 通过id
export const queryDetailBillEntryByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/tripartsettle/receiveBill/queryDetailBillEntryByMainId?id=${id}`
  })
}
// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/tripartsettle/receiveBill/delete?id=${id}`
  })
}
// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/tripartsettle/receiveBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/tripartsettle/receiveBill/importExcel', data, controller)
}

<template>
  <div
    class="contract-select-input"
    :style="{ width }"
    @click="openModal"
    @mouseenter="onmouseenter"
    @mouseleave="onmouseleave"
  >
    <span :placeholder="placeholder">{{ displayValue }}</span>
    <i class="a-icon-close-solid text-[rgba(23,43,82,0.25)]" @click.stop="handleClear" v-if="showClearBtn"></i>
    <i class="a-icon-arrow-down text-[rgba(23,43,82,0.25)]" v-else></i>
  </div>

  <a-modal
    v-model:open="visible"
    :title="title"
    width="800px"
    class="common-modal contract-select-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center mb-[16px]">
      <s-input
        v-model="params.contractNumber"
        placeholder="搜索合同编号"
        @input="handleInput"
        class="!w-[240px] mr-[16px]"
      ></s-input>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <filter-more
        :params="params"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ y: 'calc(60vh - 180px)' }"
      @change="onTableChange"
    ></a-table>
  </a-modal>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page } from '@/views/contract/management/apis'
import { renderDictTag } from '@/utils/render'

const { modelValue, multiple } = defineProps({
  modelValue: { required: true, type: [String, Array] },
  placeholder: { type: String, default: '' },
  title: { type: String, default: '选择合同' },
  multiple: { type: Boolean, default: false },
  maxCount: { type: Number, default: undefined },
  width: { type: String, default: '100%' }
})

const emit = defineEmits(['update:modelValue', 'change'])

const displayValue = ref('')

const params = reactive({
  number: undefined,
  contractNumber: undefined,
  status: 'AUDITOK',
  bizStatus: undefined,
  bizDate: undefined,
  signDate: undefined,
  customer: undefined,
  contractType: undefined,
  manageCompany: undefined,
  operator: undefined,
  operatorDepart: undefined,
  pricedType: undefined,
  startDate: undefined,
  expireDate: undefined,
  terminateDate: undefined,
  terminateReason: undefined,
  changeReason: undefined,
  originalContract: undefined,
  totalArea: undefined,
  totalRental: undefined,
  totalRemission: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  attachmentIds: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined
})
const searchList = [
  { label: '单据编码', name: 'number', type: 's-input' },
  { label: '合同编号', name: 'contractNumber', type: 's-input' },
  { label: '审核人', name: 'auditBy', type: 'user-select' },
  { label: '业务状态', name: 'bizStatus', type: 'dict-select', code: 'CT_BASE_ENUM_Contract_BizStatus' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '合同编号', name: 'contractNumber', type: 's-input' },
  { label: '合同类型', name: 'contractType', type: 'dict-select', code: 'CT_BAS_ContractType' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '签约日期', name: 'signDate', type: 'date' },
  { label: '合同开始时间', name: 'startDate', type: 'date' },
  { label: '合同结束时间', name: 'expireDate', type: 'date' },
  { label: '业务员', name: 'operator', type: 'user-select' },
  { label: '业务部门', name: 'operatorDepart', type: 'dept-tree-select' },
  { label: '定价类型', name: 'pricedType', type: 'dict-select', code: 'CT_BASE_ENUM_Contract_PricedType' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
  { label: '客户', name: 'customer', type: 'customer-select' }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(page)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

const columns = [
  { title: '合同编号', dataIndex: 'contractNumber', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 120 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_Contract_BizStatus', 'dot')
  },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160 },
  { title: '业务日期', dataIndex: 'remark', width: 100 },
  { title: '签约日期', dataIndex: 'signDate', width: 120 },
  { title: '业务人员', dataIndex: 'operator_dictText', width: 140 },
  { title: '合同类型', dataIndex: 'contractType_dictText', width: 130 },
  { title: '租金(元/月)', dataIndex: 'wyBuildingCount', width: 140 },
  { title: '开始日期', dataIndex: 'startDate', width: 120 },
  { title: '结束日期', dataIndex: 'expireDate', width: 120 }
]

const visible = ref(false)

const openModal = () => {
  visible.value = true
  onTableChange({ pageNo: 1, pageSize: 10 })
  if (Array.isArray(modelValue)) {
    selectedRowKeys.value = modelValue
  } else {
    selectedRowKeys.value = [modelValue]
  }
}

const showClear = ref(false)
const showClearBtn = computed(() => modelValue && modelValue.length > 0 && showClear.value)
const onmouseenter = () => {
  if (modelValue) {
    showClear.value = true
  }
}
const onmouseleave = () => {
  showClear.value = false
}

const handleClear = () => {
  emit('update:modelValue', multiple ? [] : '')
  emit('change', multiple ? [] : '')
  displayValue.value = ''
}

const handleConfirm = () => {
  const value = multiple ? selectedRowKeys.value : selectedRowKeys.value[0]
  displayValue.value = selectedRows.value.map((item) => item.contractNumber).join(', ')
  emit('update:modelValue', value)
  emit('change', value)
  handleCancel()
}

const handleCancel = () => {
  visible.value = false
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}

watch(
  () => modelValue,
  async (val) => {
    if (!(val && val.length)) {
      displayValue.value = ''
      clearSelection()
      return
    }
    if (!selectedRowKeys.value.length) {
      const id = Array.isArray(val) ? val.join(',') : val
      const { result } = await page({ id })
      result.records.forEach((item) => {
        selectedRowKeys.value.push(item.id)
        selectedRows.value.push(item)
      })
      displayValue.value = selectedRows.value.map((item) => item.number).join(', ')
    }
  },
  { immediate: true }
)
</script>

<style lang="less" scoped>
.contract-select-input {
  width: 100%;
  cursor: pointer;
  height: 40px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.2s;
  &:hover {
    border-color: var(--color-primary);
  }
  & > span:empty {
    &::after {
      content: attr(placeholder);
      color: rgba(0, 0, 0, 0.25);
    }
  }
}
</style>

<style lang="less">
.contract-select-modal {
  .ant-input-affix-wrapper,
  .ant-select-selector {
    border-color: #d9d9d9 !important;
  }
}
</style>

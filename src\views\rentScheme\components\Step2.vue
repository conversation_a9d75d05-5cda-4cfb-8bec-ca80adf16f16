<template>
  <div>
    <div class="flex items-center justify-between mb-[12px]">
      <strong class="text-[16px]">租赁单元</strong>
      <a-button type="primary" @click="handleAddUnit">
        <i class="a-icon-plus"></i>
        添加单元
      </a-button>
    </div>
    <a-table
      :data-source="form.rentSchemeEntryList"
      :columns="columns"
      :pagination="false"
      :scroll="{ x: 1500, y: 'calc(100vh - 348px)' }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'originalRent'">
          <a-input v-model:value="record.originalRent" placeholder="请输入原租金" :maxlength="10"></a-input>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn mr-[10px]" @click="viewUnitDetail(record)">单元详情</span>
          <a-popconfirm title="是否确认移除？" @confirm="handleRemoveUnit(index)">
            <span class="text-error cursor-pointer">移除</span>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <choose-unit ref="chooseUnitRef" @updateUnitList="updateUnitList"></choose-unit>
    <lease-unit-detail ref="leaseUnitDetailRef" :data-list="form.rentSchemeEntryList"></lease-unit-detail>
  </div>
</template>

<script setup>
import ChooseUnit from './ChooseUnit.vue'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'
import { message } from 'ant-design-vue'
import { moneyRegexp } from '@/utils/validate'

const { form } = defineProps({
  form: { type: Object, required: true }
})

const columns = [
  {
    title: '租赁单元名称',
    dataIndex: 'name',
    fixed: 'left',
    customRender: ({ record }) => record.name || record.leaseUnit_dictText
  },
  { title: '原租金', dataIndex: 'originalRent', width: 200 },
  { title: '地址', dataIndex: 'detailAddress' },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]

const chooseUnitRef = ref()
const handleAddUnit = () => {
  chooseUnitRef.value.open([...form.rentSchemeEntryList])
}
const updateUnitList = (list) => {
  form.rentSchemeEntryList = list
}

const leaseUnitDetailRef = ref()
const viewUnitDetail = (data) => {
  leaseUnitDetailRef.value.open(data)
}

const handleRemoveUnit = (index) => {
  form.rentSchemeEntryList.splice(index, 1)
}

/**
 * 校验字段是否都填写正确
 * @param opeType isSave=是否暂存 如果是暂存的话，则租赁单元可以不添加，但是如果添加了，就要校验字段是否正确
 */
const validate = (isSave = false) => {
  if (!isSave && !form.rentSchemeEntryList.length) {
    message.warning('请选择租赁单元')
    return false
  }
  const isValid = form.rentSchemeEntryList.some((item, index) => {
    if (!item.originalRent) {
      message.warning(`租赁单元第${index + 1}条请填写原租金`)
      return true
    }
    if (!moneyRegexp.test(String(item.originalRent))) {
      message.warning(
        `租赁单元第${index + 1}条原租金填写错误${String(item.originalRent).includes('.') ? '，小数点后最多2位小数' : ''}`
      )
      return true
    }
    return false
  })
  return !isValid
}

defineExpose({ validate })
</script>

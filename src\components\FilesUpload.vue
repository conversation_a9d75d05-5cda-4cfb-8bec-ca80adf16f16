<template>
  <div class="files-upload">
    <div class="files-upload-box">
      <img src="@/assets/svgs/upload.svg" width="32" height="32" class="mb-[8px]" />
      <span class="text-tertiary">点击或拖拽文件到这里上传</span>
      <input
        class="absolute w-full h-full left-[0] top-[0] cursor-pointer opacity-0"
        :class="{ '!cursor-not-allowed': isDisabled }"
        type="file"
        :accept="accept"
        :limit="limit"
        :multiple="multiple"
        :disabled="isDisabled"
        ref="inputRef"
        @change="onchange"
      />
    </div>
    <ul class="mt-[24px]" v-if="fileList.length">
      <li v-for="(item, index) in fileList" :key="item.id" class="file-li">
        <div class="remove-btn" @click="handleRemove(item, index)">×</div>
        <div class="flex items-center">
          <img :src="getFileImg(item.name)" width="36" height="36" class="mr-[8px]" />
          <div>
            <div class="line-clamp-1 mb-[4px]">{{ item.name }}</div>
            <small class="text-[12px] text-tertiary">{{ item.size }}</small>
          </div>
        </div>
        <div class="shrink-0 ml-[20px]" v-if="item.formData">
          <div class="text-error cursor-pointer" @click="uploadAgain(item)" v-if="item.error">重新上传</div>
          <div class="flex items-center" v-if="!item.error && item.progress">
            <div class="w-[240px] h-[8px] rounded-[4px] bg-[#eaf0fe]">
              <div :style="{ width: item.progress || '0' }" class="h-full bg-primary rounded-[4px]"></div>
            </div>
            <span class="text-success w-[82px] text-center ml-[12px]">
              {{ item.progress === '100%' ? '上传完成' : `上传中${item.progress}` }}
            </span>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { uploadFile, getFilesById } from '@/apis/common'
import excelSvg from '@/assets/svgs/excel.svg'
import imgSvg from '@/assets/svgs/img.svg'
import pdfSvg from '@/assets/svgs/pdf.svg'
import rarSvg from '@/assets/svgs/rar.svg'
import videoSvg from '@/assets/svgs/video.svg'
import wordSvg from '@/assets/svgs/word.svg'
import zipSvg from '@/assets/svgs/zip.svg'
import unknownSvg from '@/assets/svgs/unknown-file.svg'

const props = defineProps({
  modelValue: { required: true, type: String }, // 因所有的业务，附件上传全是id逗号拼接字符串，所以指定为string即可
  accept: { type: String, default: '.xls,.xlsx,.doc,.docx,.pdf' },
  limit: { type: Number, default: 5 }, // 限制上传数量
  fileSize: { type: Number, default: 5 }, // 限制文件大小，单位为M
  multiple: { type: Boolean, default: true },
  disabled: { type: Boolean, default: false },
  bizId: { type: String, default: '' } // 业务id，当需要附件回显时(比如编辑时回显附件)，需要传入业务id
})

const emit = defineEmits(['update:modelValue'])

// 若是单选，则modelValue有值时，input:file禁止点击
const isDisabled = computed(() => {
  if (!props.multiple && props.modelValue) return true
  return props.disabled
})

const acceptList = computed(() => props.accept.split(',').map((item) => item.replace('.', '')))

const fileList = ref([])

// 获取文件后缀名
const getFileType = (name) => {
  const arr = name.split('.')
  return arr[arr.length - 1].toLowerCase()
}

const getFileImg = (name) => {
  const type = getFileType(name)
  if (type === 'pdf') return pdfSvg
  if (type === 'rar') return rarSvg
  if (type === 'zip') return zipSvg
  if (['doc', 'docx'].includes(type)) return wordSvg
  if (['xls', 'xlsx'].includes(type)) return excelSvg
  if (['png', 'jpeg', 'jpg', 'webp', 'gif', 'bmp'].includes(type)) return imgSvg
  if (['mp4', 'avi'].includes(type)) return videoSvg
  return unknownSvg
}

// 校验所选文件是否合法
const filesIsValid = (files) => {
  if (!(files && files.length)) return false
  if (props.modelValue instanceof Array && files.length + fileList.value.length > props.limit) {
    message.warning(`最多上传${props.limit}个文件`)
    return false
  }
  for (let i = 0; i < files.length; i++) {
    if (files[i].size / 1024 / 1024 > props.fileSize) {
      message.warning(`上传文件大小不得超过${props.fileSize}MB`)
      return false
    }
    if (!acceptList.value.includes(getFileType(files[i].name))) {
      message.warning(`不支持上传格式为${getFileType(files[i].name)}的文件`)
      return false
    }
  }
  return true
}

const inputRef = ref()
const onchange = (e) => {
  const files = e.target.files
  if (!filesIsValid(files)) {
    inputRef.value.value = '' // 清空已选文件
    return
  }
  const list = Array.from(files).map((file) => {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('isAttachment', true)
    return {
      formData,
      id: Math.random().toString(),
      name: file.name,
      size: getFileSize(file.size),
      type: getFileType(file.name),
      progress: '',
      controller: new AbortController(),
      error: false // 是否上传失败
    }
  })
  if (props.multiple) {
    multipleUpload(list)
  } else {
    singleUpload(list[0])
  }
  inputRef.value.value = '' // 清空已选文件
}

// 单个文件上传
const singleUpload = async (item) => {
  try {
    fileList.value = [item]
    const { message } = await uploadFile(fileList.value[0].formData, fileList.value[0], fileList.value[0].controller)
    fileList.value[0].error = false
    fileList.value[0].id = message
    emit('update:modelValue', message)
  } catch {
    fileList.value[0].error = true
    fileList.value[0].id = ''
  }
}

// 多个文件上传
const multipleUpload = async (list) => {
  const startIndex = fileList.value.length
  // 先将本地选择的文件，添加到fileList中，以便显示进度条
  list.forEach((item) => {
    fileList.value.push(item)
  })
  const endIndex = fileList.value.length - 1
  await nextTick()
  const domList = document.querySelectorAll('.files-upload .file-li')
  domList[domList.length - 1].scrollIntoView({ behavior: 'smooth', block: 'end' })

  const tasks = []
  for (let i = startIndex; i <= endIndex; i++) {
    tasks.push(uploadFile(fileList.value[i].formData, fileList.value[i], fileList.value[i].controller))
  }
  const data = await Promise.allSettled(tasks)
  data.forEach((item, index) => {
    const fileIndex = fileList.value.findIndex((file) => file.id === list[index].id)
    // 文件上传的过程中，有可能被用户删除，所以需要判断fileIndex是否存在
    if (fileIndex !== -1) {
      if (item.status === 'fulfilled') {
        Object.assign(fileList.value[fileIndex], {
          error: false,
          id: item.value.message
        })
      } else {
        // 上传失败
        Object.assign(fileList.value[fileIndex], {
          error: true,
          id: ''
        })
      }
    }
  })
  emit(
    'update:modelValue',
    fileList.value
      .filter((item) => !item.id.includes('.'))
      .map((item) => item.id)
      .join(',')
  )
}

/**
 * 获取文件大小
 * @param {Number} bytes 前端选择文件上传时，得到的文件大小，单位是字节
 * @param {Number} isKb 由后端接口回显文件时，得到的文件大小，单位是kb，只有回显时，才有isKb这个参数
 */
const getFileSize = (bytes, isKb) => {
  if (isKb) {
    bytes *= 1024
  }
  const KB = 1024
  const MB = 1024 * 1024
  if (bytes < KB) return `${bytes}b`
  if (bytes >= MB) {
    const sizeInMB = bytes / MB
    return Number.isInteger(sizeInMB) ? `${sizeInMB}MB` : `${sizeInMB.toFixed(2)}MB`
  }
  const sizeInKB = bytes / KB
  return Number.isInteger(sizeInKB) ? `${sizeInKB}KB` : `${sizeInKB.toFixed(2)}KB`
}

// 上传失败后重新上传
const uploadAgain = async (item) => {
  try {
    item.error = false
    item.progress = ''
    const { message } = await uploadFile(item.formData, item, item.controller)
    item.id = message
  } catch {
    item.error = true
    item.progress = ''
  }
}

const removeFile = (index) => {
  fileList.value.splice(index, 1)
  emit(
    'update:modelValue',
    fileList.value
      .filter((item) => !item.id.includes('.'))
      .map((item) => item.id)
      .join(',')
  )
}

const handleRemove = (item, index) => {
  if (item.error) {
    removeFile(index)
    return
  }
  Modal.confirm({
    title: '系统提示',
    content: item.progress === '100%' ? '确认移除该文件？' : '该文件当前正在上传中，是否确认取消上传并移除？',
    centered: true,
    onOk: () => {
      if (item.progress !== '100%') {
        item.controller.abort()
      }
      removeFile(index)
    }
  })
}

watch(
  () => props.modelValue,
  (val) => {
    if (!val) {
      fileList.value = []
    }
  },
  {
    immediate: true
  }
)

watch(
  () => props.bizId,
  async (val) => {
    if (!val) return
    const data = await getFilesById(val)
    fileList.value = data.map((item) => ({
      formData: null,
      id: item.id,
      name: item.fileName,
      size: getFileSize(item.fileSizeKb, true),
      type: item.fileType,
      progress: '100%',
      controller: null,
      error: false
    }))
    emit('update:modelValue', fileList.value.map((item) => item.id).join(','))
  },
  {
    immediate: true
  }
)
</script>

<style lang="less" scoped>
.files-upload {
  &-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 110px;
    border: 1px solid #e6e9f0;
    background-color: #f7f8fa;
    border-radius: 8px;
    position: relative;
  }
  .file-li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border: 1px solid #e6e9f0;
    background-color: #fff;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 16px;
    position: relative;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .remove-btn {
    position: absolute;
    top: -4px;
    right: -4px;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #e6e9f0;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--color-secondary);
    font-weight: bold;
    line-height: 1;
  }
}
</style>

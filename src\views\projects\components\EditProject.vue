<template>
  <a-drawer
    v-model:open="visible"
    class="edit-project-drawer common-drawer"
    :title="form.id ? '编辑项目' : '新建项目'"
    :mask-closable="false"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-form
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-col="{ style: { width: '66px' } }"
      label-align="left"
      autocomplete="off"
    >
      <h2 class="text-[16px] font-bold mb-[20px]">项目基础信息</h2>
      <div class="flex gap-[40px]">
        <a-form-item label="项目名称" name="name" class="flex-1">
          <a-input v-model:value="form.name" placeholder="请输入项目名称" :maxlength="20"></a-input>
        </a-form-item>
        <a-form-item label="公司" name="company" class="flex-1">
          <dept-tree-select v-model="form.company" placeholder="请选择公司" type="company"></dept-tree-select>
        </a-form-item>
      </div>
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="form.remark"
          placeholder="请输入备注(选填)"
          show-count
          :maxlength="500"
          :auto-size="{ minRows: 4, maxRows: 4 }"
        ></a-textarea>
      </a-form-item>
    </a-form>
    <div class="flex items-center justify-between h-[32px] mb-[20px]">
      <h2 class="text-[16px] font-bold">楼栋信息</h2>
      <a-button type="primary" size="medium" @click="handleAddBuilding" v-if="form.wyBuildingPageList.length">
        <i class="a-icon-plus"></i>
        添加楼栋
      </a-button>
    </div>
    <div class="flex" v-if="form.wyBuildingPageList.length">
      <aside class="w-[240px] max-h-[calc(100vh-480px)] overflow-y-auto">
        <draggable v-model="form.wyBuildingPageList" handle=".a-icon-move" item-key="id">
          <template #item="{ element, index }">
            <div class="building-item" :class="{ active: currentBuilding.id === element.id }">
              <i
                class="a-icon-move cursor-move text-tertiary mr-[8px] text-[20px]"
                v-show="form.wyBuildingPageList.length > 1"
              ></i>
              <a-popconfirm title="是否确认移除？" @confirm="handleRemoveBuilding(index)">
                <div>
                  <i
                    class="a-icon-remove cursor-pointer text-tertiary mr-[12px] text-[20px] hover:text-error transition-colors"
                  ></i>
                </div>
              </a-popconfirm>
              <span class="line-clamp-1 flex-1" @click="handleBuildingClick(element)">
                {{ element.name }}
              </span>
            </div>
          </template>
        </draggable>
        <a-button class="w-full add-building-btn" type="primary" ghost @click="handleAddBuilding">
          <i class="a-icon-plus"></i>
          添加楼栋
        </a-button>
      </aside>
      <div class="w-[1px] bg-[#e6e9f0] mx-[24px]"></div>
      <section class="flex-1 max-h-[calc(100vh-480px)] overflow-y-auto">
        <div class="mb-[10px]">
          楼栋名称
          <span class="text-error">*</span>
        </div>
        <a-input v-model:value="currentBuilding.name" placeholder="请输入楼栋名称" :maxlength="50" show-count></a-input>
        <div class="mt-[40px] mb-[16px] flex items-center justify-between">
          <span>楼层</span>
          <a-button
            type="primary"
            size="medium"
            ghost
            @click="handleAddFloor"
            v-if="currentBuilding.wyFloorList.length"
          >
            <i class="a-icon-plus"></i>
            添加楼层
          </a-button>
        </div>
        <draggable v-model="currentBuilding.wyFloorList" handle=".a-icon-move" item-key="id">
          <template #item="{ element, index }">
            <div class="flex items-center justify-between mb-[16px]">
              <i
                class="a-icon-move cursor-move text-tertiary mr-[8px] text-[20px]"
                v-show="currentBuilding.wyFloorList.length > 1"
              ></i>
              <a-popconfirm title="是否确认移除？" @confirm="handleRemoveFloor(index)">
                <div>
                  <i class="a-icon-remove cursor-pointer text-tertiary mr-[12px] hover:text-error text-[20px]"></i>
                </div>
              </a-popconfirm>
              <a-input v-model:value="element.name" class="flex-1" placeholder="请输入楼层名称"></a-input>
            </div>
          </template>
        </draggable>
        <a-button type="primary" ghost class="w-full" @click="handleAddFloor">
          <i class="a-icon-plus"></i>
          添加楼层
        </a-button>
      </section>
    </div>
    <div v-else>
      <a-button type="primary" ghost class="w-full" @click="handleAddBuilding">
        <i class="a-icon-plus"></i>
        添加楼栋
      </a-button>
    </div>
    <template #footer>
      <a-button type="primary" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
    <a-modal
      v-model:open="modalVisible"
      title="楼栋信息"
      width="560px"
      wrap-class-name="common-modal"
      :mask-closable="false"
      centered
      @ok="handleModalConfirm"
      @cancel="handleModalCancel"
    >
      <a-form
        :model="buildingForm"
        ref="buildingFormRef"
        :rules="buildingRules"
        :label-col="{ style: { width: '84px' } }"
        autocomplete="off"
      >
        <a-form-item label="楼栋名称" name="name">
          <a-input
            v-model:value="buildingForm.name"
            placeholder="请输入楼栋名称"
            :maxlength="50"
            show-count
            @keyup.enter="handleModalConfirm"
          ></a-input>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-drawer>
</template>

<script setup>
import { addProject, editProject, queryBuilding, verifyName } from '../apis.js'
import { queryFloor } from '@/views/building/apis/building'
import draggable from 'vuedraggable'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (data) => {
  if (data) {
    Object.assign(form, {
      id: data.id,
      company: data.company,
      name: data.name,
      remark: data.remark
    })
    loadBuildingList()
  }
  visible.value = true
}

// 获取楼栋列表
const loadBuildingList = async () => {
  const { result } = await queryBuilding({ id: form.id })
  if (result && result.length) {
    form.wyBuildingPageList = result.map((item) => {
      item.wyFloorList = item.wyFloorList && item.wyFloorList.length ? item.wyFloorList : []
      return item
    })
    currentBuilding.id = form.wyBuildingPageList[0].id
    currentBuilding.name = form.wyBuildingPageList[0].name
    await loadFloorList(form.wyBuildingPageList[0])
    currentBuilding.wyFloorList = form.wyBuildingPageList[0].wyFloorList
  }
}

const loadFloorList = async (building) => {
  const { result } = await queryFloor({ id: building.id })
  if (result && result.length) {
    building.wyFloorList = result
  }
}

const form = reactive({
  id: '',
  company: '',
  name: '',
  remark: '',
  status: 'ENABLE',
  wyBuildingPageList: []
})

const validateName = async (_, value) => {
  if (!value) return Promise.reject('请输入项目名称')
  const { result } = await verifyName({
    name: value,
    id: form.id || undefined
  })
  if (result) return Promise.reject('该项目名称已存在')
  return Promise.resolve()
}
const rules = {
  name: [{ required: true, validator: validateName, trigger: 'blur' }],
  company: [{ required: true, message: '请选择公司', trigger: 'change' }]
}

const formRef = ref()
const loading = ref(false)
const handleSubmit = async () => {
  if (loading.value) return
  await formRef.value.validate()
  const index = form.wyBuildingPageList.findIndex((i) => i.id === currentBuilding.id)
  if (index !== -1) {
    Object.assign(form.wyBuildingPageList[index], currentBuilding)
  }
  const repeat = {} // 统计名称重复的楼栋数量
  form.wyBuildingPageList.forEach((item) => {
    if (repeat[item.name]) {
      repeat[item.name]++
    } else {
      repeat[item.name] = 1
    }
  })
  // 如果repeat里的每个名称数量都为1，则没有重复的楼栋名称
  if (!Object.values(repeat).every((num) => num === 1)) {
    const names = []
    for (const key in repeat) {
      if (repeat[key] > 1) {
        names.push(key)
      }
    }
    message.warning(`在该项目中，存在重复的楼栋名称【${names.join('、')}】`)
    return
  }
  const result = form.wyBuildingPageList.some((item) => {
    if (item.wyFloorList.some((i) => i.name.trim() === '')) {
      Object.assign(currentBuilding, item)
      message.warning(`楼栋“${item.name}”有楼层名称为空`)
      return true
    }
    return false
  })
  if (result) return
  try {
    loading.value = true
    const params = JSON.parse(JSON.stringify(form))
    params.wyBuildingPageList = params.wyBuildingPageList.map((item) => {
      if (item.id.includes('.')) {
        item.id = ''
      }
      item.wyFloorList.forEach((i) => {
        if (i.id.includes('.')) {
          i.id = ''
        }
      })
      return item
    })
    form.id ? await editProject(params) : await addProject(params)
    loading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    loading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.company = ''
  form.name = ''
  form.remark = ''
  form.status = 'ENABLE'
  form.wyBuildingPageList = []
  currentBuilding.id = ''
  currentBuilding.name = ''
  currentBuilding.wyFloorList = []
  formRef.value.clearValidate()
  visible.value = false
}

const modalVisible = ref(false)
const buildingForm = reactive({
  name: ''
})
const validateBuildingName = (_, value) => {
  if (!value) return Promise.reject('请输入楼栋名称')
  if (form.wyBuildingPageList.some((i) => i.name === value)) {
    return Promise.reject(`楼栋名称“${value}”已存在`)
  }
  return Promise.resolve()
}
const buildingRules = {
  name: [{ required: true, validator: validateBuildingName, trigger: 'blur' }]
}
const buildingFormRef = ref()
const handleModalConfirm = async () => {
  await buildingFormRef.value.validate()
  const data = {
    id: Math.random().toString(),
    name: buildingForm.name,
    wyFloorList: []
  }
  form.wyBuildingPageList.push(data)
  Object.assign(currentBuilding, data)
  handleModalCancel()
  await nextTick()
  document.querySelector('.edit-project-drawer .add-building-btn').scrollIntoView({ behavior: 'smooth' })
}
const handleModalCancel = () => {
  buildingFormRef.value.resetFields()
  modalVisible.value = false
}

const handleAddBuilding = () => {
  if (!currentBuilding.id) {
    modalVisible.value = true
    return
  }
  if (currentBuilding.name.trim() === '') {
    message.warning('楼栋名称不可为空')
    return
  }
  if (currentBuilding.wyFloorList.some((i) => i.name.trim() === '')) {
    message.warning('楼层名称不可为空')
    return
  }
  modalVisible.value = true
}

const handleRemoveBuilding = (index) => {
  form.wyBuildingPageList.splice(index, 1)
}

const handleBuildingClick = async (data) => {
  if (data.id === currentBuilding.id) return
  if (currentBuilding.name.trim() === '') {
    message.warning('楼栋名称不可为空')
    return
  }
  if (currentBuilding.wyFloorList.some((i) => i.name.trim() === '')) {
    message.warning('楼层名称不可为空')
    return
  }
  const building = form.wyBuildingPageList.find((i) => i.id === currentBuilding.id)
  if (building) {
    building.wyFloorList = currentBuilding.wyFloorList
  }
  if (!data.id.includes('.') && !data.wyFloorList.length) {
    await loadFloorList(data)
  }
  currentBuilding.id = data.id
  currentBuilding.name = data.name
  currentBuilding.wyFloorList = data.wyFloorList
}

const currentBuilding = reactive({
  id: '',
  name: '',
  wyFloorList: []
})

const handleAddFloor = () => {
  currentBuilding.wyFloorList.push({
    id: Math.random().toString(),
    name: ''
  })
}

const handleRemoveFloor = (index) => {
  currentBuilding.wyFloorList.splice(index, 1)
}

defineExpose({ open })
</script>

<style lang="less">
.edit-project-drawer {
  .building-item {
    display: flex;
    align-items: center;
    border: 1px solid #e6e9f0;
    background-color: #f7f8fa;
    border-radius: 8px;
    height: 40px;
    padding: 0 16px;
    margin-bottom: 16px;
    cursor: pointer;
    transition:
      border-color 0.2s,
      background-color 0.2s;
    &:hover {
      border-color: var(--color-primary);
    }
    &.active {
      border-color: var(--color-primary);
      background-color: #eaf0fe;
    }
  }
}
</style>

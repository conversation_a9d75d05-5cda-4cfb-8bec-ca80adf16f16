<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitch(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitch(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <a-dropdown>
          <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
            <span>操作</span>
            <i class="a-icon-arrow-down ml-[4px]"></i>
          </div>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="handleAudit(true)">审核(临时功能)</a-menu-item>
              <a-menu-item @click="handleViewContract">查看合同</a-menu-item>
              <a-menu-item @click="handleWithdraw" v-if="['AUDITING'].includes(detail.status)">撤回</a-menu-item>
              <a-menu-item @click="handleEdit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
                编辑
              </a-menu-item>
              <a-menu-item @click="handleRemove" v-if="['TEMP', 'BACK'].includes(detail.status)">删除</a-menu-item>
              <a-menu-item
                @click="handleAudit(detail, false)"
                v-if="['InExecution', 'Modified', 'Suspended', 'Cleared'].includes(detail.bizStatus)"
              >
                反审核
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">合同退租清算</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[16px]">基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
        <span class="w-[50%]">合同编码: {{ detail.contract_dictText }}</span>
        <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
        <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
        <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
        <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
        <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
        <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
        <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
        <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
        <span class="w-[50%]">租赁单元: {{ detail.leaseUnit_dictText }}</span>
      </div>
      <div class="w-full mb-[20px]" v-if="billList.length">
        <h4 class="text-[16px] font-bold mt-[40px] mb-[16px] w-full">清算明细</h4>
        <div class="flex gap-x-[16px]">
          <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
            <div class="flex items-center justify-between border-0 border-b border-solid border-[#e0e0e0] p-[10px]">
              <span>应收</span>
              <span>￥{{ billList[0].receiveAmount }}</span>
            </div>
            <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
              <li
                class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                v-for="item in billList[0].detailBillEntryVOList"
                :key="item.id"
              >
                <div>
                  <p>{{ item.incomeBelongYm }} {{ item.paymentType_dictText }}</p>
                  <small class="text-tertiary text-[12px]">
                    {{ item.receiveBeginDate }} - {{ item.receiveEndDate }}
                  </small>
                </div>
                <div>
                  <p class="text-right">{{ item.paymentAmount }}</p>
                  <small class="text-tertiary text-[12px]">
                    应收{{ item.actualReceiveAmount }} | 已收{{ item.paid }}
                  </small>
                </div>
              </li>
            </ul>
            <a-empty
              description="暂无应收数据"
              class="py-[20px]"
              v-show="!billList[0].detailBillEntryVOList.length"
            ></a-empty>
          </section>
          <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
            <div class="flex items-center justify-between border-0 border-b border-solid border-[#e0e0e0] p-[10px]">
              <span>应退</span>
              <span>￥{{ billList[1].refundedAmount }}</span>
            </div>
            <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
              <li
                class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                v-for="item in billList[1].detailBillEntryVOList"
                :key="item.id"
              >
                <div>
                  <p>{{ item.paymentType_dictText }}</p>
                  <small class="text-tertiary text-[12px]">{{ item.number }}</small>
                </div>
                <div>
                  <p class="text-right">{{ item.paymentAmount }}</p>
                  <small class="text-tertiary text-[12px]">
                    应收{{ item.actualReceiveAmount }} | 已收{{ item.paid }}
                  </small>
                </div>
              </li>
            </ul>
            <a-empty
              description="暂无应退数据"
              class="py-[20px]"
              v-show="!billList[1].detailBillEntryVOList.length"
            ></a-empty>
          </section>
          <section class="flex-1 border border-solid border-[#e0e0e0] rounded-[8px] overflow-hidden">
            <div class="flex items-center justify-between border-0 border-b border-solid border-[#e0e0e0] p-[10px]">
              <span>已收待核销</span>
              <span>￥{{ billList[2].paidWaitConsumedAmount }}</span>
            </div>
            <ul class="px-[10px] max-h-[50vh] overflow-y-auto scrollbar">
              <li
                class="flex items-center justify-between py-[10px] border-0 border-b border-solid border-[#e0e0e0] last-of-type:border-b-0"
                v-for="item in billList[2].payExplainBookVOList"
                :key="item.id"
              >
                <div>
                  <p>{{ item.receiveDate }}</p>
                  <small class="text-tertiary text-[12px]">{{ item.number }}</small>
                </div>
                <div>
                  <p class="text-right">{{ item.sumWaitConsumedAmt }}</p>
                  <small class="text-tertiary text-[12px]">
                    到账{{ item.sumAmt }} | 已核销{{ item.sumConsumedAmt }}
                  </small>
                </div>
              </li>
            </ul>
            <a-empty
              description="暂无已收待核销数据"
              class="py-[20px]"
              v-show="!billList[2].payExplainBookVOList.length"
            ></a-empty>
          </section>
        </div>
      </div>
      <h2 class="text-[16px] font-bold mt-[40px] mb-[16px]">违约情况</h2>
      <div class="text-secondary">
        <span>违约金额: {{ detail.liquidatedDamagesAmount }}</span>
        <p class="mt-[12px]">违约说明: {{ detail.liquidatedDamagesRemark }}</p>
      </div>
      <div class="mt-[20px]" v-if="computedResult.title">
        <strong class="text-[18px]">{{ computedResult.title }}</strong>
        <div class="mt-[16px] text-secondary">{{ computedResult.description }}</div>
      </div>
    </a-spin>
  </a-drawer>
  <contract-detail ref="contractDetailRef" :data-list="[]"></contract-detail>
</template>

<script setup>
import { detail as getDetail, deleteBatch, audit, unAudit } from '../apis.js'
import { Modal, message } from 'ant-design-vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import Decimal from 'decimal.js'

const { dataList } = defineProps({
  dataList: { required: true, type: Array }
})

const emit = defineEmits(['edit', 'refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadDetail(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const currentIndex = ref(0)
const handleSwitch = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  loadDetail(dataList[index].id)
}

const detail = reactive({})
const billList = computed(() => detail.clearingDetailBillVOList || [])
const computedResult = computed(() => {
  const data = { title: '', description: '' }
  if (!billList.value.length) return data
  const paidWaitConsumedAmount = billList.value[2].paidWaitConsumedAmount // 累计已收待核销
  const refundedAmount = billList.value[1].refundedAmount // 累计应退
  const receiveAmount = billList.value[0].receiveAmount // 累计应收
  const liquidatedDamagesAmount = detail.liquidatedDamagesAmount ? Number(detail.liquidatedDamagesAmount) : 0 // 违约金
  const result = new Decimal(paidWaitConsumedAmount)
    .plus(new Decimal(refundedAmount))
    .sub(new Decimal(receiveAmount))
    .sub(new Decimal(liquidatedDamagesAmount))
    .toNumber()
  if (result === 0) {
    data.title = '已结清'
    data.description = ''
    return data
  }
  data.title = result > 0 ? `应退: ${result}` : `合计欠款: ${result}`
  const str = liquidatedDamagesAmount ? ` - [违约金]${liquidatedDamagesAmount}` : '' // 违约金信息
  data.description = `[累计已收待核销]${paidWaitConsumedAmount} + [累计应退]${refundedAmount} - [累计应收]${receiveAmount}${str} = ${result}`
  return data
})

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  loading.value = false
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const contractDetailRef = ref()
const handleViewContract = () => {
  contractDetailRef.value.open(detail.contract)
}

const handleWithdraw = () => {}

const handleAudit = async (data, result) => {
  result ? await audit({ id: data.id }) : await unAudit({ id: data.id })
  message.success('保存成功')
  loadDetail(detail.id)
}
const handleRemove = () => {
  Modal.confirm({
    title: '确认删除该合同资料归档？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
  detail.dataFileFillDetailEntryList = [] // 账单明细
}

defineExpose({ open })
</script>

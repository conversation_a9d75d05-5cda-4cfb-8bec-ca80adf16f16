<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex justify-between !my-[24px]">
      <div class="flex">
        <a-button @click="handleImport">
          <span class="a-icon-import-right mr-[8px]"></span>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <span class="a-icon-export-right mr-[8px]"></span>
          导出
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <a-form class="!ml-[40px]" autocomplete="off" layout="inline">
          <a-form-item label="业务状态">
            <a-select class="!w-[280px]" v-model:value="search.treeId" placeholder="请选择业务状态"></a-select>
          </a-form-item>
          <a-form-item label="搜索">
            <s-input class="!w-[280px]" v-model:value="search.treeId" placeholder="请搜索"></s-input>
          </a-form-item>
          <a-form-item>
            <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
          </a-form-item>
        </a-form>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="rowView(record)">详情</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <!-- 跳转到合同详情 -->
                  <span class="primary-btn">查看合同</span>
                </a-menu-item>
                <a-menu-item>
                  <!-- 跳转到合同账单调整申请 -->
                  <span class="primary-btn">调整金额</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>

    <!-- 详情 -->
    <detail ref="detailRef" @load-data="onTableChange" :ids="tableIds || []"></detail>

    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入应收明细"
      :download-fn="() => exportExcel('应收明细数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import Detail from './components/Detail.vue'
// import { renderDict } from '@/utils/render'
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage, exportExcel, importExcel } from './apis'
// Modal,
import { message } from 'ant-design-vue'
onMounted(() => {
  onTableChange()
})
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const search = ref({})
const searchList = reactive([])
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const tableIds = computed(() => {
  return list.value.map((item) => item.id)
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id', true, true)

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 150, fixed: true },
  { title: '合同', dataIndex: 'customer_dictText' },
  { title: '物业管理公司', dataIndex: 'contractNum' },
  { title: '客户', dataIndex: 'leaseUnit' },
  { title: '业务员', dataIndex: 'paymentType' },
  { title: '业务部门', dataIndex: '' },
  { title: '业务日期', dataIndex: '' },
  { title: '单据状态', dataIndex: '' },
  { title: '账单类型', dataIndex: '' },
  { title: '业务状态', dataIndex: 'receiveAmt' },
  { title: '租赁单', dataIndex: 'consumedAmt' },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const detailRef = ref()
// 查看
const rowView = (row) => {
  detailRef?.value.open(row)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('楼栋数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>

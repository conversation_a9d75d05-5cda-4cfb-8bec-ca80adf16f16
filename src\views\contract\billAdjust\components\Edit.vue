<template>
  <a-drawer
    v-model:open="visible"
    class="edit-money-adjustment-drawer common-drawer"
    title="合同账单调整申请"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '74px' } }"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="业务时间" name="bizDate" style="width: 50%">
          <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
        </a-form-item>
        <a-form-item label="合同" name="contract">
          <a-form-item-rest>
            <contract-select v-model="form.contract" @change="loadContractDetail" width="422px"></contract-select>
          </a-form-item-rest>
        </a-form-item>
        <div class="flex flex-wrap gap-[12px] mt-[20px] text-secondary pl-[74px]" v-if="contractDetail.id">
          <span class="w-[calc(50%-6px)]">签约客户: {{ contractDetail.customer_dictText }}</span>
          <span class="w-[calc(50%-6px)]">签约日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">管理公司: {{ contractDetail.manageCompany_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同类型: {{ contractDetail.contractType_dictText }}</span>
          <span class="w-[calc(50%-6px)]">业务员: {{ contractDetail.operator_dictText }}</span>
          <span class="w-[calc(50%-6px)]">租赁单元: {{ contractDetail.customer }}</span>
          <span class="w-[calc(50%-6px)]">合同开始日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">合同结束日期: {{ contractDetail.expireDate }}</span>
        </div>
      </a-form>
      <div class="flex items-center justify-between mt-[40px] mb-[16px]">
        <h4 class="text-[16px] font-bold">调整明细</h4>
        <a-button type="primary" size="medium" @click="handleAdd">
          <i class="a-icon-plus"></i>
          添加明细
        </a-button>
      </div>
      <a-table
        :data-source="form.receiveAmountAdjustDetailBillList"
        :columns="columns"
        :pagination="false"
        bordered
        :scroll="{ x: 1400 }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'addReduceAmount'">
            <a-input-group compact style="display: flex">
              <dict-select
                v-model="record.addReduceType"
                code="CT_BASE_ENUM_ReceiveAmountAdjustDetailBill_AddReduceType"
              ></dict-select>
              <a-input v-model:value="record.addReduceAmount" :maxlength="8">
                <template #suffix>元</template>
              </a-input>
            </a-input-group>
          </template>
          <template v-if="column.dataIndex === 'adjustRemark'">
            <a-input v-model:value="record.adjustRemark"></a-input>
          </template>
        </template>
      </a-table>
      <div class="mt-[20px]" v-if="computedResult.title">
        <strong class="text-[18px]">{{ computedResult.title }}</strong>
        <div class="mt-[16px] text-secondary">{{ computedResult.description }}</div>
      </div>
      <h4 class="text-[16px] font-bold mt-[40px] mb-[16px]">附件</h4>
      <files-upload v-model="form.attachmentIds" :biz-id="form.id"></files-upload>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button type="primary" :loading="saveLoading" ghost @click="handleSave">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <choose-bill
    ref="chooseBillRef"
    :customer="contractDetail.customer"
    :current-bill-id="form.id"
    @update-bill-list="updateBillList"
  ></choose-bill>
</template>

<script setup>
import { edit, submit, detail, save, adjustDetail } from '../apis.js'
import { detail as getContractDetail, queryContractLeaseUnits } from '@/views/contract/management/apis'
import { message } from 'ant-design-vue'
import ContractSelect from '@/views/contract/management/components/ContractSelect.vue'
import ChooseBill from './ChooseBill.vue'
import { moneyRegexp } from '@/utils/validate'
import Decimal from 'decimal.js'
import dayjs from 'dayjs'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  } else {
    form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  form.id = result.id
  form.bizDate = result.bizDate
  form.contract = result.contract
  const { result: data } = await adjustDetail({ id })
  form.receiveAmountAdjustDetailBillList = data
  if (result.contract) {
    loadContractDetail(result.contract)
  }
  loading.value = false
}

const form = reactive({
  id: '',
  manageCompany: '',
  bizDate: '',
  contract: '',
  signDate: '',
  customer: '',
  contractType: '',
  operator: '',
  operatorDepart: '',
  startDate: '',
  expireDate: '',
  leaseUnit: '',
  status: '',
  actualDealAmount: '',
  remark: '',
  attachmentIds: '',
  receiveAmountAdjustDetailBillList: []
})

const rules = {
  bizDate: [{ required: true, message: '请选择业务时间', trigger: 'change' }],
  contract: [{ required: true, message: '请选择合同', trigger: 'change' }]
}

const contractDetail = reactive({
  id: '',
  customer: ''
})
const loadContractDetail = async (id) => {
  if (!id) {
    contractDetail.id = ''
    return
  }
  const { result } = await getContractDetail({ id })
  Object.assign(contractDetail, result)
  form.contractType = result.contractType
  form.customer = result.customer
  form.expireDate = result.expireDate
  form.startDate = result.startDate
  form.manageCompany = result.manageCompany
  form.signDate = result.signDate
  form.operator = result.operator
  form.operatorDepart = result.operatorDepart
  const { result: data } = await queryContractLeaseUnits({ id })
  form.leaseUnit = data.map((item) => item.leaseUnit).join(',')
}

const columns = [
  { title: '账单ID', dataIndex: 'detailBillEntry', fixed: 'left' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '款项金额', dataIndex: 'paymentAmount' },
  { title: '已收金额', dataIndex: 'consumedAmount' },
  { title: '未收金额', dataIndex: 'noConsumedAmount' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '增减金额', dataIndex: 'addReduceAmount', width: 240, fixed: 'right' },
  { title: '调整原因', dataIndex: 'adjustRemark', width: 200, fixed: 'right' }
]

const chooseBillRef = ref()
const handleAdd = () => {
  if (!contractDetail.id) {
    message.warning('请先选择合同')
    return
  }
  chooseBillRef.value.open([...form.receiveAmountAdjustDetailBillList])
}

const updateBillList = (list) => {
  list.forEach((item) => {
    form.receiveAmountAdjustDetailBillList.push({
      id: '',
      detailBillEntry: item.id,
      paymentType: item.paymentType,
      paymentType_dictText: item.paymentType_dictText,
      periodTotalPeriod: item.periodTotalPeriod,
      paymentAmount: item.paymentAmount,
      consumedAmount: item.paid,
      noConsumedAmount: item.residual,
      receiveDate: item.receiveDate,
      addReduceType: '',
      addReduceAmount: '',
      adjustRemark: ''
    })
  })
}

// 底部计算结果
const computedResult = reactive({
  title: '', // 显示应退: xx元/调整后待核销合计: xx元
  description: '' // [累计款项金额] 6030.00 - [本次减免金额] 1600.00 + [本次增加金额] 520.00 - [累计已核销] 6500.00 = -1550.00
})
watch(
  () => form.receiveAmountAdjustDetailBillList,
  (list) => {
    const isValid = list.every((item) => item.addReduceType && moneyRegexp.test(item.addReduceAmount))
    if (!list.length || !isValid) {
      computedResult.title = ''
      form.actualDealAmount = ''
      return
    }
    let total = new Decimal(0) // 累计款项金额
    let reduce = new Decimal(0) // 本次减免金额
    let add = new Decimal(0) // 本次增加金额
    let consume = new Decimal(0) // 累计已核销金额
    list.forEach((item) => {
      total = total.plus(new Decimal(item.paymentAmount))
      consume = consume.plus(new Decimal(item.consumedAmount))
      if (item.addReduceType === 'Add') {
        add = add.plus(new Decimal(Number(item.addReduceAmount)))
      } else {
        reduce = reduce.plus(new Decimal(Number(item.addReduceAmount)))
      }
    })
    const result = total.sub(reduce).plus(add).sub(consume).toNumber()
    form.actualDealAmount = result
    computedResult.title = result < 0 ? `应退: ${Math.abs(result)}` : `调整后待核销合计: ${result}`
    const totalStr = `[累计款项金额]${total.toString()}`
    const reduceStr = reduce.toNumber() > 0 ? ` - [本次减免金额]${reduce.toNumber()}` : ''
    const addStr = add.toNumber() > 0 ? ` + [本次增加金额]${add.toNumber()}` : ''
    const consumeStr = ` - [累计已核销]${consume.toNumber()}`
    computedResult.description = `${totalStr + reduceStr + addStr + consumeStr} = ${result}`
  },
  { deep: true }
)

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  const fields = []
  for (const key in rules) {
    if (form[key]) {
      fields.push(key)
    }
  }
  await formRef.value.validateFields(fields)
  try {
    saveLoading.value = true
    form.id ? await edit(form) : await save(form)
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const validateBillList = () => {
  if (!form.receiveAmountAdjustDetailBillList.length) {
    message.warning('请选择调整明细')
    return false
  }
  const illegal = form.receiveAmountAdjustDetailBillList.some((item, index) => {
    if (!item.addReduceType) {
      message.warning(`第${index + 1}条账单，请选择增减方式`)
      return true
    }
    if (!item.addReduceAmount) {
      message.warning(`第${index + 1}条账单，请输入金额`)
      return true
    }
    if (!moneyRegexp.test(item.addReduceAmount)) {
      message.warning(`第${index + 1}条账单，金额输入错误`)
      return true
    }
    return false
  })
  return !illegal
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  if (!validateBillList()) return
  try {
    confirmLoading.value = true
    await submit(form)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (Array.isArray(form[key])) {
      form[key] = []
    } else {
      form[key] = ''
    }
  }
  formRef.value.clearValidate()
  computedResult.title = ''
  computedResult.description = ''
  contractDetail.id = ''
  visible.value = false
}

onMounted(() => {
  const contractId = sessionStorage.getItem('contractId')
  if (contractId) {
    open()
    form.contract = contractId
    loadContractDetail(contractId)
    sessionStorage.removeItem('contractId')
  }
})

defineExpose({ open })
</script>

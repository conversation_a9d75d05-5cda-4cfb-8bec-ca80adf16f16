<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}转款抵扣申请`"
    class="edit-transfer-deduction-drawer common-drawer"
    placement="right"
    width="1072px"
    :confirm-loading="confirmLoading"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="业务时间" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>

        <a-form-item label="经办人" name="operator">
          <a-form-item-rest>
            <user-select v-model="formData.operator" placeholder="请选择经办人" />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="业务部门" name="operatorDepart">
          <dept-tree-select v-model:value="formData.operatorDepart" placeholder="请选择业务部门" style="width: 100%" />
        </a-form-item>

        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="4" />
        </a-form-item>
      </a-form>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c] flex justify-between">
        <span>
          转款明细
          <span class="text-[12px] font-normal text-[#8992a3]">
            即：可用于抵扣的款项，转出用于抵扣其他未收取的应收款
          </span>
        </span>
        <a-button type="primary" @click="handleAddTransferItem">添加明细</a-button>
      </h4>

      <a-table
        v-if="formData.transferDeductionReqTransferDetailList.length"
        :columns="transferColumns"
        :data-source="formData.transferDeductionReqTransferDetailList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
        :custom-row="customTransferRow"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'detailBill'">
            <i
              v-show="hoveredTransferRowId === record.id || hoveredTransferRowId === index"
              class="a-icon-remove cursor-pointer mr-2 text-red-500"
              @click="handleRemoveTransferItem(index)"
            ></i>
            {{ record.detailBill }}
          </template>
          <template v-else-if="column.dataIndex === 'thisTransferOutAmount'">
            <a-input-number
              v-model:value="record.thisTransferOutAmount"
              :min="0"
              :precision="2"
              placeholder="请输入转款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
        </template>
      </a-table>
      <div v-else class="flex flex-col items-center">
        <img src="@/assets/imgs/no-data.png" />
        <span class="text-tertiary">暂无数据</span>
      </div>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c] flex justify-between">
        <span>
          抵扣欠款明细
          <span class="text-[12px] font-normal text-[#8992a3]">即：上述转出明细的总转出金额要抵扣的未收取营收明细</span>
        </span>
        <a-button type="primary" @click="handleAddDeductionItem">添加明细</a-button>
      </h4>
      <a-table
        v-if="formData.transferDeductionReqDeductionDetailList.length"
        :columns="debtColumns"
        :data-source="formData.transferDeductionReqDeductionDetailList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
        :custom-row="customDeductionRow"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'detailBill'">
            <i
              v-show="hoveredDeductionRowId === record.id || hoveredDeductionRowId === index"
              class="a-icon-remove cursor-pointer mr-2 text-red-500"
              @click="handleRemoveDebtItem(index)"
            ></i>
            {{ record.detailBill }}
          </template>
          <template v-else-if="column.dataIndex === 'thisTransferIntoAmount'">
            <a-input-number
              v-model:value="record.thisTransferIntoAmount"
              :min="0"
              :precision="2"
              placeholder="请输入转款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
        </template>
      </a-table>
      <div v-else class="flex flex-col items-center">
        <img src="@/assets/imgs/no-data.png" />
        <span class="text-tertiary">暂无数据</span>
      </div>
      <div class="p-[16px] bg-[#f7f8fa] rounded-[8px] mt-[40px] border border-[#e6e9f0]">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-[24px] text-[#1d335c]">
              转出合计：
              <span class="text-[#f03a1d] font-bold">{{ transferOutTotal.toFixed(2) }}</span>
              转入合计：
              <span class="text-[#f03a1d] font-bold">{{ transferInTotal.toFixed(2) }}</span>
            </p>
            <p class="mt-[8px] text-[14px] text-[#495a7a]">{{ statusInfo }}</p>
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>

  <!-- 转款明细选择器 -->
  <transfer-detail-selector ref="transferDetailSelectorRef" @confirm="handleTransferDetailConfirm" />

  <!-- 抵扣欠款明细选择器 -->
  <deduction-detail-selector ref="deductionDetailSelectorRef" @confirm="handleDeductionDetailConfirm" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import {
  editTransferDeduction,
  addTransferDeduction,
  submitTransferDeduction,
  getTransferDeductionDetail,
  getDeductionDetail
} from '../apis'
import TransferDetailSelector from './TransferDetailSelector.vue'
import DeductionDetailSelector from './DeductionDetailSelector.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const transferDetailSelectorRef = ref()
const deductionDetailSelectorRef = ref()
const hoveredTransferRowId = ref(null)
const hoveredDeductionRowId = ref(null)

/**
 * 计算转出合计
 */
const transferOutTotal = computed(() => {
  return formData.transferDeductionReqTransferDetailList.reduce((total, item) => {
    return total + (item.thisTransferOutAmount || 0)
  }, 0)
})

/**
 * 计算转入合计
 */
const transferInTotal = computed(() => {
  return formData.transferDeductionReqDeductionDetailList.reduce((total, item) => {
    return total + (item.thisTransferIntoAmount || 0)
  }, 0)
})

/**
 * 计算状态文本
 */
const statusInfo = computed(() => {
  const outTotal = transferOutTotal.value
  const inTotal = transferInTotal.value
  const difference = outTotal - inTotal

  if (difference > 0) {
    return `已多收取：${difference.toFixed(2)}`
  }
  if (difference < 0) {
    return `合计欠款：${Math.abs(difference).toFixed(2)}`
  }
  return '已结清'
})

const formDataDefault = {
  bizDate: undefined,
  operatorDepart: undefined,
  operator: undefined,
  remark: undefined,
  transferDeductionReqTransferDetailList: [],
  transferDeductionReqDeductionDetailList: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务时间' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }],
  operator: [{ required: true, message: '请选择经办人' }]
}

/**
 * 转款明细表格列配置
 */
const transferColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 150 },
  { title: '合同', dataIndex: 'contract_dictText', width: 150 },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 100 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueTransferAmount', width: 120, fixed: 'right' },
  {
    title: '本次转款金额',
    dataIndex: 'thisTransferOutAmount',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisTransferOutAmount || 0
    }
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

/**
 * 抵扣欠款明细表格列配置
 */
const debtColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 160 },
  { title: '合同', dataIndex: 'contract_dictText', width: 200, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余欠款', dataIndex: 'residueDebtAmount', width: 120, fixed: 'right' },
  {
    title: '本次转入金额',
    dataIndex: 'thisTransferIntoAmount',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisTransferIntoAmount || 0
    }
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

/**
 * 打开编辑弹窗
 * @param {Object} data - 编辑的数据对象，新建时为空
 */
const open = async (data) => {
  visible.value = true

  if (data && data.id) {
    // 编辑模式，加载数据
    Object.assign(formData, data)

    try {
      confirmLoading.value = true

      // 查询转款明细
      const transferDetailRes = await getTransferDeductionDetail({ id: data.id })
      if (transferDetailRes.success && transferDetailRes.result) {
        formData.transferDeductionReqTransferDetailList = transferDetailRes.result.map((item) => ({
          ...item,
          detailBill: item.detailBill || item.number,
          detailBillEntry: item.detailBillEntry || item.id
        }))
      }

      // 查询抵扣欠款明细
      const deductionDetailRes = await getDeductionDetail({ id: data.id })
      if (deductionDetailRes.success && deductionDetailRes.result) {
        formData.transferDeductionReqDeductionDetailList = deductionDetailRes.result.map((item) => ({
          ...item,
          detailBill: item.detailBill || item.number,
          detailBillEntry: item.detailBillEntry || item.id
        }))
      }
    } catch {
      message.error('加载明细数据失败')
    } finally {
      confirmLoading.value = false
    }
  } else {
    // 新建模式，重置表单
    Object.assign(formData, formDataDefault)
  }
}

/**
 * 关闭抽屉并重置表单
 */
const handleCancel = () => {
  Object.assign(formData, formDataDefault)
  formData.transferDeductionReqTransferDetailList = []
  formData.transferDeductionReqDeductionDetailList = []
  emits('refresh')
  visible.value = false
}

/**
 * 打开转款明细选择器
 */
const handleAddTransferItem = () => {
  // 获取已选择的明细ID数组
  const selectedDetailIds = formData.transferDeductionReqTransferDetailList.map(
    (item) => item.detailBillEntry || item.id
  )
  transferDetailSelectorRef.value?.open(selectedDetailIds)
}

/**
 * 转款明细选择确认回调
 * @param {Array} selectedData - 选中的明细数据
 */
const handleTransferDetailConfirm = (selectedData) => {
  // 获取当前已存在的明细ID集合
  const existingDetailIds = new Set(
    formData.transferDeductionReqTransferDetailList.map((item) => item.detailBillEntry || item.id)
  )

  // 过滤出新的明细（不重复的）
  const newItems = selectedData
    .filter((item) => !existingDetailIds.has(item.id))
    .map((item) => ({
      ...item,
      detailBill: item.number,
      detailBillEntry: item.id,
      thisTransferOutAmount: 0,
      residueTransferAmount: item.balance || 0,
      remark: ''
    }))

  // 只添加新的明细
  if (newItems.length > 0) {
    formData.transferDeductionReqTransferDetailList.push(...newItems)
    message.success(`已添加 ${newItems.length} 条新的转款明细`)
  } else {
    message.info('所选明细均已存在，未添加新的明细')
  }
}

/**
 * 移除转款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveTransferItem = (index) => {
  formData.transferDeductionReqTransferDetailList.splice(index, 1)
}

/**
 * 自定义转款明细行属性，处理鼠标悬停事件
 * @param {Object} record - 行数据
 * @param {number} index - 行索引
 */
const customTransferRow = (record, index) => {
  return {
    onMouseenter: () => {
      hoveredTransferRowId.value = record.id || index
    },
    onMouseleave: () => {
      hoveredTransferRowId.value = null
    }
  }
}

/**
 * 打开抵扣欠款明细选择器
 */
const handleAddDeductionItem = () => {
  // 获取已选择的明细ID数组
  const selectedDetailIds = formData.transferDeductionReqDeductionDetailList.map(
    (item) => item.detailBillEntry || item.id
  )
  deductionDetailSelectorRef.value?.open(selectedDetailIds)
}

/**
 * 抵扣欠款明细选择确认回调
 * @param {Array} selectedData - 选中的明细数据
 */
const handleDeductionDetailConfirm = (selectedData) => {
  // 获取当前已存在的明细ID集合
  const existingDetailIds = new Set(
    formData.transferDeductionReqDeductionDetailList.map((item) => item.detailBillEntry || item.id)
  )

  // 过滤出新的明细（不重复的）
  const newItems = selectedData
    .filter((item) => !existingDetailIds.has(item.id))
    .map((item) => ({
      ...item,
      detailBill: item.number,
      detailBillEntry: item.id,
      thisTransferIntoAmount: 0,
      residueDebtAmount: item.residual || 0,
      remark: ''
    }))

  // 只添加新的明细
  if (newItems.length > 0) {
    formData.transferDeductionReqDeductionDetailList.push(...newItems)
    message.success(`已添加 ${newItems.length} 条新的抵扣欠款明细`)
  } else {
    message.info('所选明细均已存在，未添加新的明细')
  }
}

/**
 * 移除抵扣欠款明细行
 * @param {number} index - 要移除的行索引
 */
const handleRemoveDebtItem = (index) => {
  formData.transferDeductionReqDeductionDetailList.splice(index, 1)
}

/**
 * 自定义抵扣欠款明细行属性，处理鼠标悬停事件
 * @param {Object} record - 行数据
 * @param {number} index - 行索引
 */
const customDeductionRow = (record, index) => {
  return {
    onMouseenter: () => {
      hoveredDeductionRowId.value = record.id || index
    },
    onMouseleave: () => {
      hoveredDeductionRowId.value = null
    }
  }
}

/**
 * 验证明细数据
 */
const validateDetails = () => {
  // 验证转款明细
  for (let i = 0; i < formData.transferDeductionReqTransferDetailList.length; i++) {
    const item = formData.transferDeductionReqTransferDetailList[i]
    if (!item.thisTransferOutAmount || item.thisTransferOutAmount <= 0) {
      message.error(`转款明细第 ${i + 1} 行的本次转款金额必须大于0`)
      return false
    }

    // 校验本次转款金额不能大于剩余可转
    if (item.thisTransferOutAmount > item.residueTransferAmount) {
      message.error(`转款明细第 ${i + 1} 行的本次转款金额不能大于剩余可转金额`)
      return false
    }
  }

  // 验证抵扣欠款明细
  for (let i = 0; i < formData.transferDeductionReqDeductionDetailList.length; i++) {
    const item = formData.transferDeductionReqDeductionDetailList[i]
    if (!item.thisTransferIntoAmount || item.thisTransferIntoAmount <= 0) {
      message.error(`抵扣欠款明细第 ${i + 1} 行的本次转款金额必须大于0`)
      return false
    }

    // 校验本次转款金额不能大于剩余可转
    // if (item.thisTransferIntoAmount > item.residueDebtAmount) {
    //   message.error(`抵扣欠款明细第 ${i + 1} 行的本次转款金额不能大于剩余可转金额`)
    //   return false
    // }
  }

  return true
}

/**
 * 保存表单数据
 * @param {boolean} isTemporary - 是否为暂存，true为暂存，false为提交
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    await formRef.value?.validate()

    // 验证明细数据
    if (!validateDetails()) {
      return
    }

    // 根据操作类型选择对应的API
    const api = formData.id ? editTransferDeduction : isTemporary ? addTransferDeduction : submitTransferDeduction

    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`转款抵扣申请${action}成功`)

    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 提交表单
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.edit-transfer-deduction-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>

<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitch(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitch(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <a-dropdown>
          <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
            <span>操作</span>
            <i class="a-icon-arrow-down ml-[4px]"></i>
          </div>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="handleAudit(true)">审核(临时功能)</a-menu-item>
              <a-menu-item @click="handleWithdraw" v-if="['AUDITING'].includes(detail.status)">撤回</a-menu-item>
              <a-menu-item @click="handleEdit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
                编辑
              </a-menu-item>
              <a-menu-item @click="handleTermination" v-if="['InExecution', 'Modified'].includes(detail.bizStatus)">
                中止
              </a-menu-item>
              <a-menu-item @click="handleCancelTermination" v-if="['Suspended'].includes(detail.bizStatus)">
                取消中止
              </a-menu-item>
              <a-menu-item @click="handleRemove" v-if="['TEMP', 'BACK'].includes(detail.status)">删除</a-menu-item>
              <a-menu-item @click="handleChange" v-if="['InExecution', 'Modified'].includes(detail.bizStatus)">
                变更
              </a-menu-item>
              <a-menu-item @click="handleRenew" v-if="!['NotEffective', null, undefined].includes(detail.bizStatus)">
                续签
              </a-menu-item>
              <a-menu-item @click="handleAdjust" v-if="['InExecution'].includes(detail.bizStatus)">
                账单调整
              </a-menu-item>
              <a-menu-item @click="handleClearing" v-if="['Suspended'].includes(detail.bizStatus)">
                退租清算
              </a-menu-item>
              <a-menu-item
                @click="handleAudit(detail, false)"
                v-if="['InExecution', 'Modified', 'Suspended', 'Cleared'].includes(detail.bizStatus)"
              >
                反审核
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.customer_dictText + ' - 租赁合同' }}</h2>
        <status-tag
          dict-code="CT_BASE_ENUM_Contract_BizStatus"
          :dict-value="detail.bizStatus"
          v-if="detail.bizStatus"
        ></status-tag>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status" v-else></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #base>
          <base-info :detail="detail"></base-info>
        </template>
        <template #bill>
          <billing-detail :detail="detail"></billing-detail>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import {
  detail as getDetail,
  deleteBatch,
  queryContractLeaseUnits,
  queryContractLeaseFunds,
  queryBillList,
  audit,
  unAudit
} from '../apis.js'
import { Modal, message } from 'ant-design-vue'
import BaseInfo from './BaseInfo.vue'
import BillingDetail from './BillingDetail.vue'

const { dataList } = defineProps({
  dataList: { required: true, type: Array }
})

const router = useRouter()

const emit = defineEmits(['edit', 'refresh', 'abort', 'change', 'renew', 'withdraw', 'cancelAbort'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadData(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const currentIndex = ref(0)
const handleSwitch = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  loadData(dataList[index].id)
}

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([loadDetail(id), loadLeaseUnitList(id), loadPaymentList(id)])
  loadBillList()
  loading.value = false
}

const detail = reactive({
  id: '',
  leaseUnitsList: [], // 租赁单元
  paymentList: [], // 合同款项
  billList: [] // 账单明细
})
const loadDetail = async (id) => {
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
}

const tabList = [
  { title: '基础信息', name: 'base', showTitle: false },
  { title: '账单明细', name: 'bill' },
  { title: '核销记录', name: 'detail', showTitle: false },
  { title: '退租清算', name: 'clearing', showTitle: false }
]

const loadLeaseUnitList = async (id) => {
  const { result } = await queryContractLeaseUnits({ id })
  detail.leaseUnitsList = result
}

const loadPaymentList = async (id) => {
  const { result } = await queryContractLeaseFunds({ id })
  detail.paymentList = result
}

const loadBillList = async () => {
  const { result } = await queryBillList({
    id: detail.id,
    number: detail.number,
    contractNumber: detail.contractNumber,
    signDate: detail.signDate,
    customer: detail.customer,
    contractType: detail.contractType,
    manageCompany: detail.manageCompany,
    operator: detail.operator,
    operatorDepart: detail.operatorDepart,
    pricedType: detail.pricedType,
    startDate: detail.startDate,
    expireDate: detail.expireDate,
    terminateDate: detail.terminateDate,
    terminateReason: detail.terminateReason,
    changeReason: detail.changeReason,
    originalContract: detail.originalContract,
    totalArea: detail.totalArea,
    remark: detail.remark,
    attachmentIds: detail.attachmentIds,
    contractLeaseUnitsList: detail.leaseUnitsList, // 租赁单元
    contractLeaseFundsList: detail.paymentList
  })
  detail.billList = result.contractDetailBillsVOList
}

const handleEdit = () => {
  emit('edit', detail)
  handleClose()
}
const handleTermination = () => {
  emit('abort', detail)
}
const handleCancelTermination = () => {
  emit('cancelAbort', detail)
}
const handleRenew = () => {
  emit('renew', detail)
  handleClose()
}
const handleWithdraw = () => {
  emit('withdraw', detail)
}
const handleChange = () => {
  emit('change', detail)
  handleClose()
}
const handleAdjust = () => {
  sessionStorage.setItem('contractId', detail.id)
  handleClose()
  router.push({ path: '/contract/billAdjust' })
}
const handleClearing = () => {
  sessionStorage.setItem('contractId', detail.id)
  handleClose()
  router.push({ path: '/contract/clearing' })
}
const handleAudit = async (data, result) => {
  result ? await audit({ id: data.id }) : await unAudit({ id: data.id })
  message.success('保存成功')
  loadDetail(detail.id)
}
const handleRemove = () => {
  Modal.confirm({
    title: '确认删除该合同？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
  detail.leaseUnitsList = [] // 租赁单元
  detail.paymentList = [] // 合同款项
  detail.billList = [] // 账单明细
}

defineExpose({ open, loadData, visible })
</script>

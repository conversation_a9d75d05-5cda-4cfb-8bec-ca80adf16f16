<template>
  <div>
    <div class="flex justify-between">
      <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
      <a-button type="primary" ghost>
        <span class="a-icon-statistics mr-[8px]"></span>
        统计分析
      </a-button>
    </div>

    <div class="!my-[24px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item label="客户">
          <user-select
            class="!w-[280px]"
            v-model="search.customerId"
            v-model:display-value="search.customerId_dictText"
            placeholder="请选择客户"
            title="请选择客户"
          />
        </a-form-item>
        <a-form-item label="公司">
          <dept-tree-select
            v-model="search.collectionCompanyId"
            placeholder="请选择公司"
            class="!w-[280px]"
            @select="collectionCompanyIdSelectChange"
          ></dept-tree-select>
        </a-form-item>
        <a-form-item label="月份">
          <a-date-picker
            class="w-[280px]"
            v-model:value="search.incomeBelongYm"
            picker="month"
            value-format="YYYY-MM"
            format="YYYY-MM"
            placeholder="请选择月份"
          />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" @click="onTableChange">查询</a-button>
          <a-button @click="searchReset">重置</a-button>
        </a-form-item>
      </a-form>
    </div>

    <div class="flex justify-center relative pb-[24px]">
      <div class="h-[32px] text-[16px] font-bold flex items-center">
        <span v-if="search.collectionCompanyId && search.incomeBelongYm">
          {{ tableTitle }} - {{ search.incomeBelongYm }}账单
        </span>
      </div>
      <div class="absolute top-0 right-0">
        <a-button type="primary" ghost :loading="exportLoading" @click="handleExport">导出账单</a-button>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="defaultColumns"
      :loading="tableLoading"
      :scroll="{ y: tableHeight }"
      :pagination="false"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    ></a-table>
    <div class="flex flex-col items-end mt-[20px] p-[20px] bg-[#F5F5F5] font-bold text-[14px] rounded-[8px]">
      <div>
        <span class="w-[120px] inline-block">含税金额合计：</span>
        <span class="w-[120px] text-end inline-block">{{ totalData.totalReceiveAmt }}</span>
      </div>
      <div class="mt-[10px] mb-[10px]">
        <span class="w-[120px] inline-block">已核销金额合计：</span>
        <span class="w-[120px] text-end inline-block">{{ totalData.totalConsumedAmt }}</span>
      </div>
      <div>
        <span class="w-[120px] inline-block">未核销金额合计：</span>
        <span class="w-[120px] text-end inline-block">{{ totalData.totalNotConsumedAmt }}</span>
      </div>
    </div>
  </div>
</template>
<script setup>
import useTableSelection from '@/hooks/useTableSelection'
import usePageTable from '@/hooks/usePageTable'
import { getPage, exportExcel } from './apis'
import { message } from 'ant-design-vue'

const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const search = ref({
  customerId: '',
  incomeBelongYm: '',
  collectionCompanyId: ''
})
const defaultColumns = [
  { title: '客户', dataIndex: 'customer', width: 150, fixed: true },
  { title: '款项类型', dataIndex: 'paymentType' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '开始日期', dataIndex: 'receiveBeginDate' },
  { title: '结束日期', dataIndex: 'receiveEndDate' },
  { title: '收入归属年月', dataIndex: 'incomeBelongYm' },
  { title: '合同号', dataIndex: 'contractNum' },
  { title: '含税金额', dataIndex: 'receiveAmt' },
  { title: '已核销金额', dataIndex: 'consumedAmt' },
  { title: '未核销金额', dataIndex: 'notConsumedAmt' }
]

const { list, tableLoading, onTableList, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')
const onTableChange = () => {
  if (search.value.collectionCompanyId && search.value.incomeBelongYm) {
    return onTableList(search.value)
  }
  message.warning('请先搜索公司和月份！')
}
const tableTitle = ref('')
// 选择公司的回调
const collectionCompanyIdSelectChange = (id, data) => {
  tableTitle.value = data.departName
}
const totalData = computed(() => {
  const obj = {
    totalReceiveAmt: 0,
    totalConsumedAmt: 0,
    totalNotConsumedAmt: 0
  }
  list.value.forEach((item) => {
    obj.totalReceiveAmt += item.receiveAmt
    obj.totalConsumedAmt += item.consumedAmt
    obj.totalNotConsumedAmt += item.notConsumedAmt
  })
  return obj
})
// 重置
const searchReset = () => {
  Object.assign(search.value, { customerId: '', incomeBelongYm: '', collectionCompanyId: '' })
  onTableChange()
}

// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('账单查询数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>

设置水电分摊信息
<template>
  <div>
    <a-table :data-source="list" :columns="columns" :pagination="false" :scroll="{ y: '50vh', x: 2100 }">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.dataIndex === 'shareType'">
          <div class="flex items-center">
            <a-popconfirm title="是否确认移除？" @confirm="handleRemoveWaterElectricity(index)">
              <div>
                <i class="a-icon-remove cursor-pointer text-error mr-[12px] text-[16px]"></i>
              </div>
            </a-popconfirm>
            <dict-select
              v-model="record.shareType"
              code="CT_BASE_ENUM_WaterShareBill_ShareType"
              width="140px"
            ></dict-select>
          </div>
        </template>
        <template v-if="column.dataIndex === 'waterEleTableNum'">
          <a-select
            show-search
            v-model:value="record.waterEleTableNum"
            :options="waterElectricityList"
            :filter-option="(input, option) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0"
            allow-clear
            style="width: 100%"
            @change="onWaterElectricityChange($event, record)"
          ></a-select>
        </template>
        <template v-if="column.dataIndex === 'doubleRate'">
          <span v-if="record.selectedObj.doubleRate">{{ record.doubleRate }}</span>
          <a-input v-else v-model:value="record.doubleRate"></a-input>
        </template>
        <template v-if="column.dataIndex === 'price'">
          <span v-if="record.selectedObj.price">{{ record.price }}</span>
          <a-input v-else v-model:value="record.price">
            <template #suffix>元</template>
          </a-input>
        </template>
      </template>
    </a-table>
    <a-button @click="handleAddWaterElectricity" type="primary" ghost size="medium" class="mt-[16px]">
      <i class="a-icon-plus"></i>
      增加一行
    </a-button>
  </div>
</template>

<script setup>
import { getWaterElectricityList } from '@/views/waterElectricity/manage/apis/waterElectricity.js'
import { message } from 'ant-design-vue'
import { moneyRegexp } from '@/utils/validate'

const { list } = defineProps({
  list: { required: true, type: Array }
})

const columns = [
  { title: '分摊类别', dataIndex: 'shareType', width: 200, fixed: 'left' },
  { title: '编码(表号)', dataIndex: 'waterEleTableNum' },
  { title: '类型', dataIndex: 'type' }, // 由水电表号数据带来的字段回显，不需要传给后端
  { title: '属性', dataIndex: 'property' }, // 由水电表号数据带来的字段回显，不需要传给后端
  { title: '倍率', dataIndex: 'doubleRate' },
  { title: '单价', dataIndex: 'price' },
  { title: '损耗量计算公式', dataIndex: 'ullageQuantity' },
  { title: '单位分摊计算公式', dataIndex: 'unitShare' },
  { title: '公摊金额计算公式', dataIndex: 'shareAmount' },
  { title: '减免金额计算公式', dataIndex: 'remission' },
  { title: '不含税合计计算公式', dataIndex: 'totalAmount', width: 170 },
  { title: '税金计算公式', dataIndex: 'taxAmount' },
  { title: '含税合计计算公式', dataIndex: 'containTaxTotalAmount' }
]

const waterElectricityList = ref([]) // 水电表列表
const loadWaterElectricityList = async () => {
  const { result } = await getWaterElectricityList({ pageNo: 1, pageSize: 10000 })
  waterElectricityList.value = result.records.map((item) => {
    item.label = `${item.name}(${item.number})`
    item.value = item.id
    return item
  })
}

const onWaterElectricityChange = (val, record) => {
  if (!val) {
    record.type = ''
    record.property = ''
    record.doubleRate = ''
    record.price = ''
    record.selectedObj = {} // 选中的水电表数据
    return
  }
  const data = waterElectricityList.value.find((i) => i.id === val)
  record.selectedObj = data
  record.type = data.type_dictText
  record.property = data.property_dictText
  record.doubleRate = data.doubleRate ? String(data.doubleRate) : ''
  record.price = data.price ? String(data.price) : ''
}

const handleAddWaterElectricity = () => {
  list.push({
    relationType: 'WyBuilding', // 项目/楼栋/楼层/租赁单元 CT_BASE_ENUM_WaterShareFormulaRelations_RelationType
    shareType: '',
    waterEleTableNum: '',
    doubleRate: '',
    price: '',
    ullageQuantity: '',
    unitShare: '',
    shareAmount: '',
    remission: '',
    totalAmount: '',
    taxAmount: '',
    containTaxTotalAmount: '',
    selfAmount: '',
    type: '', // 前端自定义字段，由水电表号数据带来的字段回显，不需要传给后端
    property: '', // 前端自定义字段，由水电表号数据带来的字段回显，不需要传给后端
    selectedObj: {} // 前端自定义字段，选中的水电表数据
  })
}
const handleRemoveWaterElectricity = (index) => {
  list.splice(index, 1)
}

const validate = () => {
  if (!list.length) return true
  return !list.some((item, index) => {
    if (!item.shareType) {
      message.warning(`第${index + 1}条数据请选择分摊类别`)
      return true
    }
    if (!item.waterEleTableNum) {
      message.warning(`第${index + 1}条数据请选择表号`)
      return true
    }
    if (!item.selectedObj.doubleRate) {
      if (item.doubleRate && !moneyRegexp.test(item.doubleRate)) {
        message.warning(`第${index + 1}条数据倍率填写不正确`)
        return true
      }
      if (item.price && !moneyRegexp.test(item.price)) {
        message.warning(`第${index + 1}条数据单价填写不正确`)
        return true
      }
    }
    return false
  })
}

onMounted(() => {
  loadWaterElectricityList()
})

defineExpose({ validate })
</script>

<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" placement="right" width="1072px" @close="handleClose">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitch(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitch(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn" @click="handleEdit">编辑</span>
          <span class="primary-btn" @click="handleUpdateStatus">
            {{ detail.status === 'ENABLE' ? '禁用' : '启用' }}
          </span>
          <span class="primary-btn" @click="handleDelete">删除</span>
        </div>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">{{ detail.name }}</h2>
        <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
      <div class="text-secondary flex flex-wrap gap-y-[12px]">
        <span class="w-1/2">名称: {{ detail.name }}</span>
        <span class="w-1/2">
          状态:
          <status-tag dict-code="CT_BASE_ENUM_BaseStatus" :dict-value="detail.status" type="dot"></status-tag>
        </span>
        <span class="w-1/2">创建人: {{ detail.createBy_dictText }}</span>
        <span class="w-1/2">创建时间: {{ detail.createTime }}</span>
      </div>
      <h2 class="text-[16px] font-bold mt-[40px] mb-[12px]">递增方式</h2>
      <ul>
        <li v-for="item in detail.priceIncreaseWayEntryList" :key="item.id" class="mb-[12px] last-of-type:mb-[0]">
          {{ item.content }}
        </li>
      </ul>
    </a-spin>
  </a-drawer>
</template>

<script setup>
import { detail as getDetail, wayList, updateStatus, deleteBatch } from '../apis.js'
import { Modal, message } from 'ant-design-vue'

// dataList: 外面表格数据，用于上一条/下一条
const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const currentIndex = ref(0)
const handleSwitch = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  loadDetail(dataList[index].id)
}

const emit = defineEmits(['edit', 'refresh'])

const visible = ref(false)
const open = (id) => {
  visible.value = true
  loadDetail(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const loading = ref(false)
const detail = reactive({
  id: '',
  name: '',
  status: 'ENABLE',
  createBy: '',
  createTime: '',
  priceIncreaseWayEntryList: []
})
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await getDetail({ id })
  Object.assign(detail, result)
  const { result: list } = await wayList({ id })
  detail.priceIncreaseWayEntryList = list.map((item, index) => ({
    ...item,
    content: `${index + 1}. 第${item.yearMonthPeriod}${item.yearMonthPeriodWay_dictText}${item.increase}%`
  }))
  loading.value = false
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const handleUpdateStatus = () => {
  Modal.confirm({
    title: `确认${detail.status === 'ENABLE' ? '禁用' : '启用'}该楼栋？`,
    content: detail.status === 'ENABLE' ? '禁用后将无法再被使用，但不影响已创建的数据。' : '',
    centered: true,
    onOk: async () => {
      await updateStatus({
        ids: detail.id,
        status: detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      })
      message.success(detail.status === 'ENABLE' ? '已禁用' : '启用成功')
      detail.status = detail.status === 'ENABLE' ? 'DISABLE' : 'ENABLE'
      emit('refresh')
    }
  })
}

const handleDelete = () => {
  Modal.confirm({
    title: '确认删除该楼栋？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
}

defineExpose({ open })
</script>

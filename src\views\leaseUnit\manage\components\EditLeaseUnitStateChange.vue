<template>
  <a-drawer
    v-model:open="visible"
    class="common-drawer"
    :title="`${formData.id ? '编辑' : '添加'}租赁单元状态变更`"
    placement="right"
    width="1072px"
    :mask-closable="false"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">变更基础信息</h4>
      <a-form ref="basicFormRef" :model="formData" :label-col="{ style: { width: '140px' } }" :rules="rules">
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="物业管理公司" name="manageCompany">
              <dept-tree-select
                v-model="formData.manageCompany"
                placeholder="请选择物业管理公司"
                type="company"
              ></dept-tree-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="变更目标状态" name="destStatus">
              <dict-select
                v-model="formData.destStatus"
                placeholder="请选择目标状态"
                code="CT_BASE_ENUM_LeaseUnit_BizStatus"
              ></dict-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="到期日期" name="bizDate">
              <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="变更说明" name="remark">
              <a-textarea v-model:value="formData.remark" placeholder="变更说明" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex justify-between items-center mb-4">
        <h4 class="text-[16px] font-bold mb-[20px] text-[#1d335c]">租赁单元</h4>
        <a-button
          v-if="
            formData.leaseUnitStateChangeReqBillEntryList && formData.leaseUnitStateChangeReqBillEntryList.length > 0
          "
          type="primary"
          @click="handleAddLeaseUnit"
        >
          <template #icon><i class="a-icon-plus"></i></template>
          添加单元
        </a-button>
      </div>
      <!-- 当有数据时显示表格 -->
      <a-table
        v-if="formData.leaseUnitStateChangeReqBillEntryList && formData.leaseUnitStateChangeReqBillEntryList.length > 0"
        class="lease-unit-table"
        :columns="columns"
        :data-source="formData.leaseUnitStateChangeReqBillEntryList"
        :pagination="false"
        row-key="id"
        :scroll="{ x: 1200 }"
        :custom-row="customRow"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'leaseUnit'">
            <i
              v-show="hoveredRowId === record.id"
              class="a-icon-remove cursor-pointer mr-2"
              @click="handleDeleteLeaseUnit(record)"
            ></i>
            {{ record.leaseUnit }}
          </template>
          <template v-if="column.dataIndex === 'bizStatus'">
            <a-tag color="success" v-if="record.bizStatus === '在租'">{{ record.bizStatus }}</a-tag>
            <a-tag v-else>{{ record.bizStatus }}</a-tag>
          </template>
        </template>
      </a-table>

      <!-- 当没有数据时显示添加按钮 -->
      <a-button v-else type="primary" @click="handleAddLeaseUnit" class="w-full">
        <template #icon><i class="a-icon-plus"></i></template>
        添加租赁单元
      </a-button>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">提交</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- 租赁单元选择弹窗 -->
  <lease-unit-select ref="leaseUnitSelectRef" @select-change="handleLeaseUnitChange" />
</template>

<script setup>
import { message, Modal } from 'ant-design-vue'
import { addLeaseUnitStateChangeReqBill, getLeaseUnitStateChangeReqBillById } from '@/views/leaseUnit/stateChange/apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const basicFormRef = ref()
const leaseUnitSelectRef = ref()
const hoveredRowId = ref()

const formDataDefault = {
  id: undefined,
  manageCompany: undefined,
  destStatus: undefined,
  bizDate: undefined,
  remark: undefined,
  leaseUnitStateChangeReqBillEntryList: []
}
const formData = reactive({ ...formDataDefault })

const rules = {
  manageCompany: [{ required: true, message: '请选择物业管理公司', trigger: 'change' }],
  destStatus: [{ required: true, message: '请选择变更目标状态', trigger: 'change' }],
  bizDate: [{ required: true, message: '请选择到期日期', trigger: 'change' }],
  remark: [{ required: true, message: '请输入变更说明', trigger: 'blur' }]
}

const columns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'leaseUnitAddress', width: 160, ellipsis: true },
  { title: '租赁归集公司', dataIndex: 'collectionCompany_dictText', width: 160, ellipsis: true },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText', width: 160, ellipsis: true },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '原业务状态', dataIndex: 'bizStatus', width: 120 },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 }
]

/**
 * 打开编辑抽屉
 * @param {Object|Array} data - 编辑数据或初始租赁单元列表
 */
const open = async (data) => {
  if (data && data.id) {
    // 编辑模式
    Object.assign(formData, data)
    const { result } = await getLeaseUnitStateChangeReqBillById({ id: data.id })
    if (result && result.length > 0) {
      formData.leaseUnitStateChangeReqBillEntryList = result
    }
  } else if (Array.isArray(data)) {
    // 新增模式，传入了初始租赁单元
    formData.leaseUnitStateChangeReqBillEntryList = data.map((unit) => ({
      id: unit.id,
      leaseUnit: unit.name,
      leaseUnitAddress: unit.detailAddress,
      collectionCompany: unit.collectionCompany,
      ownerCompany: unit.ownerCompany,
      manageCompany: unit.manageCompany,
      areaManager: unit.areaManager,
      bizStatus: unit.bizStatus,
      effectDate: unit.effectDate,
      expireDate: unit.expireDate,
      collectionCompany_dictText: unit.collectionCompany_dictText,
      ownerCompany_dictText: unit.ownerCompany_dictText,
      areaManager_dictText: unit.areaManager_dictText
    }))
  }
  visible.value = true
}

/**
 * 添加租赁单元
 */
const handleAddLeaseUnit = () => {
  // 传入当前已选择的租赁单元列表，避免重复选择
  leaseUnitSelectRef.value.open(formData.leaseUnitStateChangeReqBillEntryList)
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  // 只有在正式保存时才进行表单验证
  if (!isTemporary) {
    await basicFormRef.value.validateFields()
  }

  confirmLoading.value = true

  try {
    await addLeaseUnitStateChangeReqBill(formData)
    const action = isTemporary ? '暂存' : '保存'
    message.success(`${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 保存表单
 */
const handleSave = () => saveData(false)

/**
 * 暂存表单
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 删除租赁单元
 * @param {Object} record - 要删除的租赁单元记录
 */
const handleDeleteLeaseUnit = (record) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除租赁单元"${record.leaseUnit}"吗？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // 从表单数据中移除该租赁单元
      const index = formData.leaseUnitStateChangeReqBillEntryList.findIndex((unit) => unit.id === record.id)
      if (index !== -1) {
        formData.leaseUnitStateChangeReqBillEntryList.splice(index, 1)
        message.success(`已删除租赁单元"${record.leaseUnit}"`)
      }
    }
  })
}

/**
 * 租赁单元选择确认
 * @param {Array} selectedUnits - 选中的租赁单元列表
 */
const handleLeaseUnitChange = (selectedUnits) => {
  formData.leaseUnitStateChangeReqBillEntryList = selectedUnits.map((unit) => ({
    id: unit.id,
    leaseUnit: unit.name,
    leaseUnitAddress: unit.detailAddress,
    collectionCompany: unit.collectionCompany,
    ownerCompany: unit.ownerCompany,
    manageCompany: unit.manageCompany,
    areaManager: unit.areaManager,
    bizStatus: unit.bizStatus,
    effectDate: unit.effectDate,
    expireDate: unit.expireDate,
    collectionCompany_dictText: unit.collectionCompany_dictText,
    ownerCompany_dictText: unit.ownerCompany_dictText,
    areaManager_dictText: unit.areaManager_dictText
  }))
  message.success(`已选择 ${selectedUnits.length} 个租赁单元`)
}

/**
 * 自定义行属性，处理鼠标悬停事件
 * @param {Object} record - 行数据
 */
const customRow = (record) => {
  return {
    onMouseenter: () => {
      hoveredRowId.value = record.id
    },
    onMouseleave: () => {
      hoveredRowId.value = null
    }
  }
}

/**
 * 取消编辑
 */
const handleCancel = () => {
  visible.value = false
  basicFormRef.value?.resetFields()
  Object.assign(formData, formDataDefault)
  emits('refresh')
}

defineExpose({ open })
</script>

<style scoped>
.lease-unit-table {
  margin-top: 16px;
}

.lease-unit-table :deep(.ant-table-thead > tr > th) {
  background-color: #f5f5f5;
  font-weight: 500;
}

.lease-unit-table :deep(.ant-table-tbody > tr > td) {
  padding: 12px 16px;
}

.lease-unit-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: #f0f7ff;
}

.lease-unit-table :deep(.ant-tag) {
  border-radius: 4px;
}
</style>

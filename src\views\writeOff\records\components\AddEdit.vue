<template>
  <a-drawer
    class="common-drawer"
    v-model:open="visible"
    title="收付款核销"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleCancel"
  >
    <div class="flex">
      <div class="w-[71%] pr-[40px] !h-[calc(100vh-185px)] overflow-auto no-scrollbar">
        <a-form
          :model="ruleForm"
          ref="formRef"
          :rules="rules"
          :label-col="{ style: { width: '120px' } }"
          autocomplete="off"
        >
          <h2 class="text-[16px] font-bold mb-[12px]">基础信息</h2>
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="核销方案" name="consumeScheme">
                <api-select
                  v-model="ruleForm.consumeScheme"
                  :async-fn="() => getPage({ pageNo: 1, pageSize: 10000 })"
                  :field-names="{ label: 'schemeName', value: 'id' }"
                  placeholder="请选择核销方案"
                ></api-select>
              </a-form-item>
            </a-col>

            <a-col :span="12">
              <a-form-item label="正负记录优先对冲" name="hedging">
                <a-switch v-model:checked="ruleForm.hedging" />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="核销日期" name="consumeDate">
                <a-date-picker
                  class="w-[100%]"
                  v-model:value="ruleForm.consumeDate"
                  picker="date"
                  value-format="YYYY-MM-DD"
                  format="YYYY-MM-DD"
                  placeholder="请选择核销日期"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <div class="flex justify-between items-center mb-[12px] mt-[16px]">
            <h2 class="text-[16px] font-bold">选择收付款记录</h2>
            <a-button type="primary" ghost @click="addRecord">
              <span class="a-icon-plus mr-[8px]"></span>
              添加
            </a-button>
          </div>
          <div class="mb-[20px]" v-for="(item, index) in ruleForm.recDataEntryList" :key="index">
            <div class="flex justify-between items-center mb-[10px]">
              <span class="a-icon-remove text-[20px] text-[#8992A3]"></span>
              <user-select
                title="选择用户"
                class="!w-[96%]"
                v-model="item.number"
                placeholder="请选择用户"
              ></user-select>
            </div>
            <div
              class="flex flex-wrap gap-y-[12px] text-secondary p-[10px] rounded-[8px] border border-[#e6e9f0] bg-[#E6E9F0]"
            >
              <span class="w-[50%]">收付款时间：{{ item.receiveDate || '-' }}</span>
              <span class="w-[50%]">客户：{{ item.customer_dictText || '-' }}</span>
              <span class="w-[50%]">实际来款人：{{ item.actualPayor || '-' }}</span>
              <span class="w-[50%]">收付款公司：{{ item.collectionCompany || '-' }}</span>
              <span class="w-[50%]">合计收付金额：{{ item.sumAmt || '-' }}</span>
              <span class="w-[50%]">已核销金额：{{ item.consumedAmt || '-' }}</span>
            </div>
            <div></div>
          </div>

          <div class="flex justify-between items-center mb-[12px] mt-[40px]">
            <h2 class="text-[16px] font-bold">拟核销应收账单</h2>
            <a-button type="primary" ghost @click="addPay">
              <span class="a-icon-plus mr-[8px]"></span>
              添加
            </a-button>
          </div>

          <div class="mb-[20px]" v-for="(item, index) in ruleForm.payDataEntryList" :key="index">
            <div class="flex justify-between items-center">
              <span class="a-icon-remove text-[20px] text-[#8992A3] mb-[24px]"></span>
              <user-select
                title="选择用户"
                class="!w-[48%] !mb-[24px]"
                v-model="item.number"
                placeholder="请选择用户"
              ></user-select>
              <a-form-item
                class="w-[47%] !pl-[40px]"
                label="本次核销(元)"
                :name="['recordsList', index, 'thisConsumedAmt']"
                :rules="{
                  required: true,
                  message: '请输入本次核销',
                  trigger: 'blur'
                }"
              >
                <a-input-number class="!w-[100%]" v-model:value="item.b" placeholder="请输入本次核销"></a-input-number>
              </a-form-item>
            </div>
            <div
              class="flex flex-wrap gap-y-[12px] text-secondary p-[10px] rounded-[8px] border-[#e6e9f0] bg-[#E6E9F0]"
            >
              <span class="w-[50%]">合同：{{ item.contractNum || '-' }}</span>
              <span class="w-[50%]">款项类型：{{ item.paymentType || '-' }}</span>
              <span class="w-[50%]">租赁单元：{{ item.leaseUnit || '-' }}</span>
              <span class="w-[50%]">应收时间：{{ item.receiveDate || '-' }}</span>
              <span class="w-[50%]">实际应收金额：{{ item.receiveAmt || '-' }}</span>
              <span class="w-[50%]">已核销金额：{{ item.consumedAmt || '-' }}</span>
            </div>
            <div></div>
          </div>
        </a-form>
      </div>
      <div class="w-[29%]">
        <div class="inline-block w-[100%] bg-[#E6E9F0] p-[16px] rounded-[8px]">
          <h1 class="text-[18px] text-[#1D335C] font-bold mb-[16px]">核销结果预览</h1>
          <div class="rounded-[8px] bg-[#fff] mb-[16px] p-[12px]">
            <h2 class="text-[16px] text-[#1D335C] font-bold mb-[12px]">收付款</h2>
            <div class="text-secondary">
              <div class="mb-[12px]">收付款总金额：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">已核销金额：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">剩余可核销金额：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">本次拟核销金额：{{ previewInfo.name || '-' }}</div>
              <div>核销后剩余金额：{{ previewInfo.name || '-' }}</div>
            </div>
          </div>
          <div class="rounded-[8px] bg-[#fff] p-[12px]">
            <h2 class="text-[16px] text-[#1D335C] font-bold mb-[12px]">应收账单</h2>
            <div class="text-secondary">
              <div class="mb-[12px]">轻工大厦A2012-租金-202：{{ previewInfo.name || '-' }}</div>
              <div class="mb-[12px]">轻工大厦A2012-物业费-202：{{ previewInfo.name || '-' }}</div>
              <div>轻工大厦A2012-水费-202：{{ previewInfo.name || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <a-button type="primary" @click="handleConfirm">核销</a-button>
      <a-button type="primary" ghost @click="handleStash">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
  <!-- 选择收付款记录 -->
  <receive-pay-record-select
    ref="receivePayRecordSelectRef"
    :async-func="getUnConsumedPage"
    :params="unConsumedPagePar"
    @selectChange="recordSelectChange"
  ></receive-pay-record-select>
  <!-- 选择明细账单 -->
  <receive-certificate-select
    :async-func="getReceiveBillUnConsumedPage"
    :table-columns="tableColumns"
    :params="receiveBillUnConsumedPagePar"
    ref="receiveCertificateSelectRef"
    @selectChange="paySelectChange"
  ></receive-certificate-select>
</template>
<script setup>
import { getPage } from '@/views/writeOff/solution/apis.js'
import ReceivePayRecordSelect from '@/components/ReceivePayRecordSelect.vue'
import ReceiveCertificateSelect from '@/components/ReceiveCertificateSelect.vue'
import { message } from 'ant-design-vue'
import { consumed, preview } from '../apis.js'
import { getUnConsumedPage } from '@/views/receivePayRecords/apis.js'
import { getReceiveBillUnConsumedPage } from '@/views/statement/receiveCertificate/apis.js'
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  if (data.id) {
    ruleForm.recDataEntryList.push({
      number: data.number,
      receiveDate: data.receiveDate,
      customer: data.customer,
      customer_dictText: data.customer_dictText,
      actualPayor: data.actualPayor,
      collectionCompany: data.manageCompany_dictText,
      collectionCompanyId: data.manageCompany,
      sumAmt: data.sumAmt,
      consumedAmt: data.actualReceiveAmt
    })
  }
  visible.value = true
  // loadMenuList()
}
defineExpose({ open })
const tableColumns = [
  { title: '单据编号', dataIndex: 'number', width: 180, fixed: true },
  { title: '客户名称', dataIndex: 'customer' },
  { title: '合同', dataIndex: 'contractNum' },
  { title: '租赁单元', dataIndex: 'leaseUnit' },
  { title: '款项类型', dataIndex: 'paymentType' },
  { title: '结算状态', dataIndex: '' },
  { title: '期数/总期数', dataIndex: '' },
  { title: '款项金额', dataIndex: '' },
  { title: '减免金额', dataIndex: '' },
  { title: '应收金额', dataIndex: 'receiveAmt' },
  { title: '已收金额', dataIndex: 'consumedAmt' },
  { title: '剩余金额', dataIndex: 'notConsumedAmt' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '结清日期', dataIndex: '' }
]
// 未核销的收付款记录请求参数
const unConsumedPagePar = ref({
  receiveDate: '',
  receiveBeginDate: '',
  receiveEndDate: '',
  number: '',
  customer: '',
  customerId: '',
  collectionCompany: '',
  collectionCompanyId: '',
  paymentType: '',
  paymentTypeId: '',
  incomeBelongYm: '',
  leaseUnit: '',
  contractNum: '',
  operatorDepart: '',
  operatorDepartId: '',
  operator: '',
  operatorId: '',
  serviceCenter: '',
  park: '',
  carportNum: '',
  receiveAmt: '',
  consumedAmt: '',
  notConsumedAmt: '',
  thisConsumedAmt: '',
  sourceBillId: '',
  sourceBillEntryId: ''
})
const receiveBillUnConsumedPagePar = ref({ bizStatus: '', customer: '' })

const previewInfo = ref({})
const ruleForm = reactive({
  consumeScheme: '',
  hedging: false,
  consumeDate: '',
  recDataEntryList: [],
  payDataEntryList: [],
  schemeObj: {
    id: '',
    manageCompany: '',
    schemeName: '',
    schemeNumber: '',
    collectionCompanySame: true,
    customerSame: true,
    paymentTypeSame: true,
    leaseUnitSame: true,
    contractNumberSame: true,
    operatorDepartSame: true,
    operatorSame: true,
    serviceCenterSame: true,
    parkSame: true,
    carportNumSame: true,
    remark: '',
    createBy: '',
    createTime: '',
    updateBy: '',
    updateTime: '',
    auditBy: '',
    auditTime: '',
    attachmentIds: '',
    sourceBillId: '',
    sourceBillEntryId: '',
    ctrlUnit: '',
    delFlag: 0
  }
})
const rules = computed(() => {
  return {
    consumeScheme: [{ required: true, message: '请选择核销方案', trigger: ['change'] }],
    consumeDate: [{ required: true, message: '请选择核销日期', trigger: ['change'] }]
  }
})

// 添加收付款记录
const receivePayRecordSelectRef = ref()
const addRecord = () => {
  receivePayRecordSelectRef?.value.open()
}
// 选择收付款记录回调
const recordSelectChange = (list) => {
  list.forEach((item) => {
    ruleForm.recDataEntryList.push({
      id: '',
      number: '',
      receiveDate: item.receiveDate,
      customer: item.customer,
      customer_dictText: item.customer_dictText,
      actualPayor: item.actualPayor,
      collectionCompany: item.manageCompany_dictText,
      collectionCompanyId: item.manageCompany,
      receiveAmt: item.sumAmt,
      consumedAmt: item.actualReceiveAmt
      // receiveBeginDate: item.receiveBeginDate,
      // receiveEndDate: item.receiveEndDate,
      // number: item.number,
      // customerId: item.customerId,
      // paymentType: item.paymentType,
      // paymentTypeId: item.paymentTypeId,
      // incomeBelongYm: item.incomeBelongYm,
      // leaseUnit: item.leaseUnit,
      // contractNum: item.contractNum,
      // operatorDepart: item.operatorDepart,
      // operatorDepartId: item.operatorDepartId,
      // operator: item.operator,
      // operatorId: item.operatorId,
      // serviceCenter: item.serviceCenter,
      // park: item.park,
      // carportNum: item.carportNum,
      // notConsumedAmt: 0,
      // thisConsumedAmt: 0,
      // sourceBillId: item.sourceBillId,
      // sourceBillEntryId: item.sourceBillEntryId
    })
  })
}

const receiveCertificateSelectRef = ref()
const addPay = () => {
  receiveCertificateSelectRef?.value.open()
}

// 选择明细账单回调
const paySelectChange = (list) => {
  list.forEach((item) => {
    ruleForm.payDataEntryList.push({
      id: '',
      number: '',
      receiveDate: item.receiveDate,
      paymentType: item.paymentType,
      paymentTypeId: item.paymentTypeId,
      leaseUnit: item.leaseUnit,
      contractNum: item.contractNum,
      receiveAmt: item.receiveAmt,
      consumedAmt: item.consumedAmt,
      // 需要用户填写的
      thisConsumedAmt: ''

      // receiveBeginDate: '',
      // receiveEndDate: '',
      // number: '',
      // customer: '',
      // customerId: '',
      // collectionCompany: '',
      // collectionCompanyId: '',
      // incomeBelongYm: '',
      // operatorDepart: '',
      // operatorDepartId: '',
      // operator: '',
      // operatorId: '',
      // serviceCenter: '',
      // park: '',
      // carportNum: '',
      // notConsumedAmt: 0,
      // sourceBillId: '',
      // sourceBillEntryId: ''
    })
  })
}

// 预览
const previewData = ref({})
const getPreviewData = async () => {
  const data = await preview(ruleForm)
  message.success(data.message)
  previewData.value = data.result
}

// 核销
const formRef = ref()
const handleConfirm = async () => {
  await formRef.value.validate()
  const data = await consumed(ruleForm)
  message.success(data.message)
}

// 暂存
const handleStash = () => {
  getPreviewData()
}
// 取消
const handleCancel = () => {
  formRef.value.clearValidate()
  visible.value = false
}
</script>

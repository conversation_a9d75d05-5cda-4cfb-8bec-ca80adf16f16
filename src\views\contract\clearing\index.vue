<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.name"
          placeholder="搜索名称"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleAudit(record, true)">审核(临时功能)</a-menu-item>
                <a-menu-item @click="handleViewContract(record)">查看合同</a-menu-item>
                <a-menu-item @click="handleWithdraw(record)" v-if="['AUDITING'].includes(record.status)">
                  撤回
                </a-menu-item>
                <a-menu-item @click="handleEdit(record)" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  编辑
                </a-menu-item>
                <a-menu-item @click="handleRemove(record)" v-if="['TEMP', 'BACK'].includes(record.status)">
                  删除
                </a-menu-item>
                <a-menu-item
                  @click="handleAudit(record, false)"
                  v-if="['InExecution', 'Modified', 'Suspended', 'Cleared'].includes(record.bizStatus)"
                >
                  反审核
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit ref="editRef" @refresh="refresh"></edit>
    <detail ref="detailRef" :data-list="list" @refresh="refreshFromDetail" @edit="handleEdit"></detail>
    <contract-detail ref="contractDetailRef" :data-list="[]"></contract-detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('退租清算导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit, withdraw } from './apis.js'
import Edit from './components/Edit.vue'
import Detail from './components/Detail.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag } from '@/utils/render'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'

const route = useRoute()

const pageTitle = computed(() => route.meta.title)

const params = reactive({
  id: undefined,
  number: undefined,
  manageCompany: undefined,
  bizStatus: undefined,
  status: undefined,
  closingDate: undefined,
  handler: undefined,
  handlerDepart: undefined,
  clearingType: undefined,
  totalAmount: undefined,
  totalReceivable: undefined,
  totalDeductible: undefined,
  contract: undefined,
  signDate: undefined,
  customer: undefined,
  contractType: undefined,
  operator: undefined,
  operatorDepart: undefined,
  startDate: undefined,
  expireDate: undefined,
  leaseUnit: undefined,
  isLiquidatedDamages: undefined,
  liquidatedDamagesAmount: undefined,
  liquidatedDamagesRemark: undefined,
  actualDealAmount: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined
})
const searchList = [
  { label: '客户', name: 'customer', type: 'customer-select' },
  { label: '合同编号', name: 'contractNumber', type: 's-input' },
  // { label: '应收金额', name: 'contractNumber', type: 'input' },
  // { label: '未收金额', name: 'contractNumber', type: 'input' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '业务状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '清算类型', name: 'clearingType', type: 'dict-select', code: 'CT_BASE_ENUM_Clearing_ClearingType' },
  { label: '管理公司', name: 'manageCompany', type: 'company-select' },
  { label: '计费截止时间', name: 'closingDate', type: 'date' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '创建时间', name: 'signDate', type: 'date' },
  { label: '备注', name: 'remark', type: 's-input' }
]

const defaultColumns = [
  { title: '单据编号', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 120 },
  { title: '合同编号', dataIndex: 'contract_dictText', width: 100 },
  { title: '应收金额', dataIndex: 'receiveAmount', width: 160 },
  { title: '应退金额', dataIndex: 'refundedAmount', width: 160 },
  { title: '已收待核销金额', dataIndex: 'paidWaitConsumedAmount', width: 160 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_Clearing_BizStatus', 'dot')
  },
  { title: '清算类型', dataIndex: 'clearingType_dictText', width: 140 },
  { title: '管理公司', dataIndex: 'manageCompany_dictText', width: 130 },
  { title: '租赁单元', dataIndex: 'leaseUnit_dictText', width: 130 },
  { title: '计费截止时间', dataIndex: 'closingDate', width: 130 },
  { title: '业务日期', dataIndex: 'bizDate', width: 130 },
  { title: '创建时间', dataIndex: 'createTime', width: 140 },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editRef = ref()
const handleAdd = () => {
  editRef.value.open()
}
const handleEdit = (data) => {
  editRef.value.open(data.id)
}

const detailRef = ref()
const handleView = (data) => {
  detailRef.value.open(data.id)
}

const contractDetailRef = ref()
const handleViewContract = (data) => {
  contractDetailRef.value.open(data.contract)
}

const handleWithdraw = (data) => {
  Modal.confirm({
    title: '是否确认撤回该合同退租清算申请？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await withdraw({ id: data.id })
      message.success('已撤回')
      onTableChange(pagination.value)
    }
  })
}

const handleAudit = async (data, result) => {
  result ? await audit({ id: data.id }) : await unAudit({ id: data.id })
  message.success('保存成功')
  onTableChange(pagination.value)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除该合同退租清算？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('退租清算数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>

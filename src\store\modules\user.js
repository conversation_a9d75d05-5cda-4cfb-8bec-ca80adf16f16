import { login, queryPermissions, logout } from '@/apis/user'
import router, { createRouters } from '@/router'
import { useDictStore } from './dict'

export const useUserStore = defineStore('user', {
  persist: {
    key: `${import.meta.env.VITE_STORE_KEY}:user`
  },
  state: () => ({
    departs: [],
    token: '',
    userInfo: {},
    permission: {
      menu: []
    },
    currentMenu: {
      title: '',
      list: []
    }
  }),
  actions: {
    async loginByUsername(params) {
      const { result } = await login(params)
      this.$patch((state) => {
        state.departs = result.departs || []
        state.token = result.token
        state.userInfo = result.userInfo
      })
      await this.getPermissions()

      // 登录成功后加载字典数据
      const dictStore = useDictStore()
      await dictStore.getAllDict()
    },
    async getPermissions() {
      const { result } = await queryPermissions()
      this.permission = result
      createRouters(result.menu)
    },
    setCurrentMenus(data) {
      this.currentMenu.title = data.title
      this.currentMenu.list = data.list
    },
    async logout() {
      await logout()
      this.$patch((state) => {
        state.departs = []
        state.token = ''
        state.userInfo = {}
        state.permission = { menu: [] }
      })
      router.replace('/login')
    }
  }
})

export default function userStore() {
  const store = useUserStore()
  return storeToRefs(store)
}

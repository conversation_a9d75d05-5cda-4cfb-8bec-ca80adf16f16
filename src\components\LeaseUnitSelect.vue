<template>
  <a-modal
    v-model:open="visible"
    :title="title || '选择租赁单元'"
    width="1072px"
    :mask-closable="false"
    class="common-modal"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <!-- 搜索区域 -->
    <div class="mb-[12px]">
      <s-input v-model="searchParams.name" placeholder="搜索名称" class="!w-[280px]" @input="handleSearch"></s-input>
      <filter-more
        :params="searchParams"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="handleFilterSearch"
      ></filter-more>
    </div>

    <!-- 已选择提示 -->
    <div class="mb-[12px] px-[12px] py-[8px] bg-blue-50 rounded border border-blue-200" v-if="multiple">
      <span class="text-blue-600">已选择 {{ selectedCount }} 个租赁单元</span>
      <span class="primary-btn ml-2 !text-gray-400" @click="handleClearAll" v-if="selectedCount > 0">清空选择</span>
    </div>

    <!-- 表格区域 -->
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        selectedRows,
        onChange: onSelectChange,
        type: multiple ? 'checkbox' : 'radio'
      }"
      :scroll="{ x: 1500, y: tableHeight }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_AuditStatus" type="dot"></status-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import { getLeaseUnitList } from '@/views/leaseUnit/manage/apis/leaseUnit'
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getUserList } from '@/views/system/user/apis'
import { projectPage } from '@/views/projects/apis.js'
import { getPage } from '@/views/assets/manage/apis'
import region from '@/json/region.json'

const { multiple, asyncFunc } = defineProps({
  multiple: { default: true, type: Boolean },
  asyncFunc: { default: '', type: [Function, String] },
  title: { default: '', type: String }
})

const emits = defineEmits(['selectChange'])

const visible = ref(false)

const searchParams = reactive({
  name: undefined,
  pcaCode: [],
  useType: undefined,
  leaseUse: undefined,
  areaManager: undefined,
  status: undefined,
  bizStatus: undefined,
  supportFacility: undefined,
  effectDate: undefined,
  expireDate: undefined,
  layerNum: undefined,
  propertyUse: undefined,
  wyProject: undefined,
  number: undefined
})

const searchList = reactive([
  {
    label: '区域',
    name: 'pcaCode',
    type: 'cascader',
    placeholder: '请选择区域',
    options: region,
    fieldNames: { label: 'label', value: 'value', children: 'children' }
  },
  { label: '使用类型', name: 'useType', type: 'dict-select', placeholder: '请选择使用类型', code: 'CT_BAS_UseType' },
  { label: '租赁用途', name: 'leaseUse', type: 'dict-select', code: 'CT_BAS_LeaseUse', placeholder: '请选择租赁用途' },
  {
    label: '片区管理员',
    name: 'areaManager',
    type: 'api-select',
    placeholder: '请选择片区管理员',
    asyncFn: getUserList
  },
  {
    label: '单据状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择单据状态'
  },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_LeaseUnit_BizStatus',
    placeholder: '请选择业务状态'
  },
  { label: '配套设施', name: 'supportFacility', type: 'input', placeholder: '请输入配套设施' },
  { label: '生效日期', name: 'effectDate', type: 'date', placeholder: '请选择生效日期' },
  { label: '到期日期', name: 'expireDate', type: 'date', placeholder: '请选择到期日期' },
  { label: '层数/总层数', name: 'layerNum', type: 'input', placeholder: '请输入层数' },
  {
    label: '产权用途',
    name: 'propertyUse',
    type: 'dict-select',
    code: 'CT_BAS_PropertyUse',
    placeholder: '请选择产权用途'
  },
  { label: '所属项目', name: 'wyProject', type: 'api-select', placeholder: '请选择所属项目', asyncFn: projectPage },
  { label: '关联资产', name: 'houseOwner', type: 'api-select', placeholder: '请选择关联资产', asyncFn: getPage },
  { label: '单元编码', name: 'number', type: 'input', placeholder: '请输入单元编码' }
])

const columns = [
  { title: '租赁单元名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 160, ellipsis: true },
  { title: '使用类型', dataIndex: 'useType_dictText', width: 120 },
  { title: '租赁面积(m²)', dataIndex: 'leaseArea', width: 120 },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText', width: 120 },
  { title: '片区管理员', dataIndex: 'areaManager_dictText', width: 160, ellipsis: true },
  { title: '单据状态', dataIndex: 'status', width: 120 },
  { title: '业务状态', dataIndex: 'bizStatus_dictText', width: 120 },
  { title: '配套设施', dataIndex: 'supportFacility', width: 160, ellipsis: true },
  { title: '生效日期', dataIndex: 'effectDate', width: 120 },
  { title: '到期日期', dataIndex: 'expireDate', width: 120 },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '产权用途', dataIndex: 'propertyUse_dictText', width: 120 },
  { title: '所属项目', dataIndex: 'wyProject_dictText', width: 120 },
  { title: '单元编码', dataIndex: 'number', width: 200 }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(asyncFunc || getLeaseUnitList)
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const selectedCount = computed(() => selectedRows.value.length)

/**
 * 打开选择器
 */
const open = (selectedUnits = []) => {
  onTableChange()
  if (selectedUnits && selectedUnits.length > 0) {
    const selectedIds = selectedUnits.map((unit) => unit.id)
    selectedRowKeys.value = selectedIds
    // 根据 id 从 list 中获取完整的对象数据
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  visible.value = true
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  handleCancel()
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}

/**
 * 清空所有选择
 */
const handleClearAll = () => {
  clearSelection()
}

/**
 * 搜索处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

/**
 * 筛选搜索处理
 */
const handleFilterSearch = () => {
  pagination.value.current = 1
  onTableChange()
}

/**
 * 表格变化处理
 */
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...searchParams })
}

defineExpose({ open })
</script>

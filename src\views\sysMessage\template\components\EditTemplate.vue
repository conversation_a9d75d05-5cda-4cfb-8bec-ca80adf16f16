<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}消息模板`"
    class="edit-template-drawer common-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h2 class="text-[16px] font-bold my-[24px]">基础信息</h2>
      <a-form ref="formRef" :model="formData" :rules="rules">
        <a-form-item label="模版标题" name="templateName">
          <a-input v-model:value="formData.templateName" placeholder="请输入模板标题" />
        </a-form-item>
        <a-form-item label="模板类型" name="templateType">
          <a-select v-model:value="formData.templateType" placeholder="请选择模板类型" allow-clear>
            <a-select-option value="plainText">纯文本</a-select-option>
            <a-select-option value="richText">富文本</a-select-option>
          </a-select>
        </a-form-item>
        <h2 class="text-[16px] font-bold my-[24px]">模版内容</h2>
        <a-form-item name="templateContent" class="form-item-full">
          <t-editor v-model="formData.templateContent" :height="400" :maximum="50000" />
        </a-form-item>
      </a-form>
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">保存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addTemplate, updateTemplate } from '../apis'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()

const formDataDefault = {
  id: undefined,
  templateName: undefined,
  templateContent: undefined,
  templateType: undefined
}

const formData = reactive({ ...formDataDefault })

const rules = {
  templateName: [{ required: true, message: '请输入模板标题', trigger: 'blur' }],
  templateType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
  templateContent: [{ required: true, message: '请输入模板内容', trigger: 'blur' }]
}

/**
 * 打开抽屉
 */
const open = async (data) => {
  confirmLoading.value = true
  visible.value = true

  await nextTick()

  if (data && data.id) {
    Object.assign(formData, data)
  }
  confirmLoading.value = false
}

/**
 * 取消编辑
 */
const handleCancel = () => {
  emits('refresh')
  Object.assign(formData, formDataDefault)
  if (formRef.value) {
    formRef.value.clearValidate()
  }
  visible.value = false
}

/**
 * 保存数据
 */
const handleSave = async () => {
  await formRef.value.validate()
  confirmLoading.value = true

  const api = formData.id ? updateTemplate : addTemplate

  try {
    await api(formData)

    const action = formData.id ? '编辑' : '添加'
    message.success(`消息模版${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

defineExpose({ open })
</script>

<style lang="less">
.edit-template-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }
}
</style>

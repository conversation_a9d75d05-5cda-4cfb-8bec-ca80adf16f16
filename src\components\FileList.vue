<!-- 附件展示组件 -->
<template>
  <div>
    <div
      :style="{ display: 'grid', gridTemplateColumns: `repeat(${columns}, 1fr)`, gap: '16px' }"
      v-if="fileList.length"
    >
      <div
        class="flex items-center justify-between py-[8px] px-[12px] bg-[#f7f8fa] border border-[#e6e9f0] rounded-[8px]"
        v-for="item in fileList"
        :key="item.id"
      >
        <div class="flex items-center mr-[10px]">
          <img :src="getIconUrl(item.fileType)" width="28" height="28" />
          <div class="text-secondary ml-[12px]">
            <p class="line-clamp-1 mb-[2px]" :title="item.fileName">{{ item.fileName }}</p>
            <small class="text-[12px]">{{ item.fileSizeKb }}kb</small>
          </div>
        </div>
        <a :href="item.link" class="a-icon-download text-[20px] !text-primary" target="_blank"></a>
      </div>
    </div>
    <div class="flex flex-col items-center" v-if="!fileList.length && showEmpty">
      <img src="@/assets/imgs/no-data.png" />
      <span class="text-tertiary">未上传附件</span>
    </div>
  </div>
</template>

<script setup>
import { getFilesById } from '@/apis/common'

const { bizId } = defineProps({
  bizId: { required: true, type: String },
  columns: { type: Number, default: 3 }, // 栅格布局，分为几列，默认是3列
  showEmpty: { type: Boolean, default: true } // 没有附件的时候，是否显示空状态
})

const fileList = ref([])

watch(
  () => bizId,
  async (val) => {
    if (!val) return
    const data = await getFilesById(val)
    fileList.value = data
  },
  {
    immediate: true
  }
)

const getIconUrl = (type) => {
  let svgUrl = ''
  switch (type) {
    case 'pdf':
      svgUrl = '/src/assets/svgs/pdf.svg'
      break
    case 'doc':
    case 'docx':
      svgUrl = '/src/assets/svgs/word.svg'
      break
    case 'xls':
    case 'xlsx':
      svgUrl = '/src/assets/svgs/excel.svg'
      break
    case 'rar':
      svgUrl = '/src/assets/svgs/rar.svg'
      break
    case 'zip':
      svgUrl = '/src/assets/svgs/rar.svg'
      break
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'bmp':
    case 'webp':
      svgUrl = '/src/assets/svgs/img.svg'
      break
    case 'mp4':
    case 'avi':
      svgUrl = '/src/assets/svgs/video.svg'
      break
    default:
      svgUrl = '/src/assets/svgs/unknown-file.svg'
  }
  return new URL(svgUrl, import.meta.url).href
}
</script>

<template>
  <a-drawer
    v-model:open="visible"
    class="edit-archive-type-drawer common-drawer"
    :title="form.id ? '编辑归档类型' : '新建归档类型'"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '46px' } }"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="名称" name="name">
          <a-input v-model:value="form.name" :maxlength="50" show-count></a-input>
        </a-form-item>
        <a-form-item label="状态" name="status">
          <dict-select v-model="form.status" code="CT_BASE_ENUM_BaseStatus"></dict-select>
        </a-form-item>
        <a-form-item label="说明" name="remark">
          <a-textarea
            v-model:value="form.remark"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 4, maxRows: 4 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
      <div class="flex items-center justify-between mt-[40px] mb-[20px]">
        <h2 class="text-[16px] font-bold">归档类型列表</h2>
        <a-button size="medium" type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          添加类型
        </a-button>
      </div>
      <a-table
        :data-source="form.fileFillTypeEntryList"
        :columns="columns"
        :pagination="false"
        :scroll="{ y: 'calc(100vh - 574px)' }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
          <template v-if="column.dataIndex === 'materialType'">
            <dict-select v-model="record.materialType" code="CT_BAS_MaterialType"></dict-select>
          </template>
          <template v-if="column.dataIndex === 'submitPerson'">
            <user-select v-model="record.submitPerson"></user-select>
          </template>
          <template v-if="column.dataIndex === 'receivePerson'">
            <user-select v-model="record.receivePerson"></user-select>
          </template>
          <template v-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" :maxlength="200"></a-input>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-popconfirm title="是否确认移除？" @confirm="handleRemove(index)">
              <span class="text-error cursor-pointer">移除</span>
            </a-popconfirm>
          </template>
        </template>
      </a-table>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, add, detail, queryTypeList } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    if (key !== 'fileFillTypeEntryList') {
      form[key] = result[key]
    }
  }
  const { result: list } = await queryTypeList({ id })
  form.fileFillTypeEntryList = list
  loading.value = false
}

const form = reactive({
  id: '',
  name: '',
  status: 'ENABLE',
  remark: '',
  fileFillTypeEntryList: []
})

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }]
}

const columns = [
  { title: '序号', dataIndex: 'index', width: 80, fixed: 'left' },
  { title: '材料类别', dataIndex: 'materialType' },
  { title: '提交人', dataIndex: 'submitPerson' },
  { title: '接收人', dataIndex: 'receivePerson' },
  { title: '备注', dataIndex: 'remark' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]
const handleAdd = () => {
  form.fileFillTypeEntryList.push({
    materialType: '',
    submitPerson: '',
    receivePerson: '',
    remark: ''
  })
}
const handleRemove = (index) => {
  form.fileFillTypeEntryList.splice(index, 1)
}

// 校验类型列表是否正确填写
const validateTypeList = () => {
  if (!form.fileFillTypeEntryList.length) {
    message.warning('请至少添加一个归档类型')
    return false
  }
  const illegal = form.fileFillTypeEntryList.some((item, index) => {
    if (!item.materialType) {
      message.warning(`第${index + 1}条类型，请选择材料类别`)
      return true
    }
    if (!item.submitPerson) {
      message.warning(`第${index + 1}条类型，请选择提交人`)
      return true
    }
    if (!item.receivePerson) {
      message.warning(`第${index + 1}条类型，请选择接收人`)
      return true
    }
    return false
  })
  return !illegal
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  if (!validateTypeList()) return
  try {
    confirmLoading.value = true
    form.id ? await edit(form) : await add(form)
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  form.id = ''
  form.name = ''
  form.status = 'ENABLE'
  form.remark = ''
  form.fileFillTypeEntryList = []
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-archive-type-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 40px;
  }
  .ant-form-item {
    width: calc(50% - 20px);
    &:last-child {
      width: 100%;
    }
  }
}
</style>

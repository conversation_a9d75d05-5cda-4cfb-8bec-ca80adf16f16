<template>
  <a-modal
    v-model:open="visible"
    title="资产盘活信息"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <a-form
        :model="ruleForm"
        ref="formRef"
        :rules="rules"
        :label-col="{ style: { width: '150px' } }"
        autocomplete="off"
      >
        <a-form-item label="盘活措施填报日期" name="fillDate">
          <a-date-picker
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            v-model:value="ruleForm.fillDate"
            type="date"
            placeholder="请选择盘活措施填报日期"
            class="w-[100%]"
          />
        </a-form-item>
        <a-form-item label="已采取的盘活管理措施" name="ctrlMeasure">
          <a-textarea
            v-model:value="ruleForm.ctrlMeasure"
            placeholder="请输入已采取的盘活管理措施"
            :maxlength="500"
            show-count
          />
        </a-form-item>
        <a-form-item label="下一步盘活建议" name="activateAdvise">
          <a-textarea
            v-model:value="ruleForm.activateAdvise"
            placeholder="请输入下一步盘活建议"
            :maxlength="500"
            show-count
          />
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { idleEdit, occupyEdit } from '../apis'
import { message } from 'ant-design-vue'
const emit = defineEmits(['refresh'])
const visible = ref(false)
const submitParams = ref({})
const open = (params, tableList, data = {}) => {
  submitParams.value = params
  if (submitParams.value.trackingType === 'Idle') {
    submitParams.value.idleAssetActivateList = tableList
  } else {
    submitParams.value.occupyAssetActivateList = tableList
  }
  visible.value = true
  if (data.id) {
    ruleForm.value = data
  }
}
defineExpose({ open })

const ruleForm = ref({
  id: '',
  fillDate: '',
  ctrlMeasure: '',
  activateAdvise: ''
})
const rules = {
  classPath: [{ required: true, message: '请输入类路径', trigger: 'blur' }],
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  classKey: [{ required: true, message: '请输入类key', trigger: 'blur' }]
}
const formRef = ref()
const confirmLoading = ref(false)

const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    if (submitParams.value.trackingType === 'Idle') {
      // 编辑
      if (ruleForm.value.id) {
        submitParams.value.idleAssetActivateList.forEach((item) => {
          if (ruleForm.value.id === item.id) {
            Object.assign(item, ruleForm.value)
          }
        })
      } else {
        // 新增
        submitParams.value.idleAssetActivateList.push(ruleForm.value)
      }
    } else {
      // 编辑
      if (ruleForm.value.id) {
        submitParams.value.occupyAssetActivateList.forEach((item) => {
          if (ruleForm.value.id === item.id) {
            Object.assign(item, ruleForm.value)
          }
        })
      } else {
        // 新增
        submitParams.value.occupyAssetActivateList.push(ruleForm.value)
      }
    }
    const data = await (submitParams.value.trackingType === 'Idle'
      ? idleEdit(submitParams.value)
      : occupyEdit(submitParams.value))
    confirmLoading.value = false
    handleCancel()
    message.success(data.message)
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}
const resetForm = () => {
  ruleForm.value.id = ''
  ruleForm.value.fillDate = ''
  ruleForm.value.ctrlMeasure = ''
  ruleForm.value.activateAdvise = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}
</script>

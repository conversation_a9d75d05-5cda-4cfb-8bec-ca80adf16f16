设置水电分摊信息弹窗
<template>
  <a-modal
    v-model:open="visible"
    title="设置楼层水电分摊信息"
    width="1200px"
    wrap-class-name="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex items-center justify-between mb-[16px]">
      <h2 class="text-[16px] font-bold">楼层水电分摊信息</h2>
      <div class="flex items-center">
        <span>自动出账时间: 每月</span>
        <a-input
          v-model:value="floorInfo.everyMonthAutoBillDay"
          class="!w-[60px] !mx-[10px]"
          size="medium"
          :maxlength="2"
        ></a-input>
        <span>日</span>
      </div>
    </div>
    <set-info :list="floorInfo.waterShareFormulas" ref="setInfoRef"></set-info>
  </a-modal>
</template>

<script setup>
import SetInfo from './SetInfo.vue'

const emit = defineEmits(['updateFloorWaterShareFormulas'])

const visible = ref(false)

const floorInfo = reactive({
  id: '',
  everyMonthAutoBillDay: '',
  waterShareFormulas: []
})
const open = (info) => {
  floorInfo.id = info.id
  floorInfo.everyMonthAutoBillDay = info.everyMonthAutoBillDay
  floorInfo.waterShareFormulas = [...info.waterShareFormulas]
  visible.value = true
}

const setInfoRef = ref()
const handleConfirm = () => {
  if (setInfoRef.value.validate()) {
    emit('updateFloorWaterShareFormulas', floorInfo)
    visible.value = false
  }
}
const handleCancel = () => {
  visible.value = false
}

defineExpose({ open })
</script>

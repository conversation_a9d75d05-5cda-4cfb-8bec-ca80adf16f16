import { createApp } from 'vue'
import App from './App.vue'
import router from '@/router'
import store from '@/store'
import '@/styles/tailwind.css'
import '@/styles/reset.less'
import '@/styles/iconfont.less'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import VueCropper from 'vue-cropper'
import 'vue-cropper/dist/index.css'
import { permission } from '@/utils/directives'
dayjs.locale('zh-cn')

const app = createApp(App)
app.use(VueCropper)
app.use(store)
app.use(router)
app.directive('permission', permission)

app.mount('#app')

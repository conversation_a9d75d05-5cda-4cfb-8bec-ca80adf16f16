<template>
  <a-drawer
    v-model:open="visible"
    class="edit-file-archiving-drawer common-drawer"
    title="合同资料归档"
    placement="right"
    width="1072px"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <h2 class="text-[16px] font-bold mb-[20px]">基础信息</h2>
      <a-form
        :model="form"
        :rules="rules"
        ref="formRef"
        :label-col="{ style: { width: '74px' } }"
        label-align="left"
        autocomplete="off"
      >
        <a-form-item label="业务时间" name="bizDate">
          <a-date-picker v-model:value="form.bizDate" value-format="YYYY-MM-DD" style="width: 100%"></a-date-picker>
        </a-form-item>
        <a-form-item label="归档部门" name="fileFillDepart">
          <dept-tree-select v-model="form.fileFillDepart"></dept-tree-select>
        </a-form-item>
        <a-form-item label="归档类型" name="fileFillType">
          <api-select v-model="form.fileFillType" :async-fn="getTypeList" @change="handleTypeChange"></api-select>
        </a-form-item>
        <a-form-item label="合同" name="contract" class="form-item-full">
          <a-form-item-rest>
            <contract-select v-model="form.contract" @change="loadContractDetail" width="402px"></contract-select>
          </a-form-item-rest>
        </a-form-item>
        <div class="flex flex-wrap gap-[12px] text-secondary pl-[74px]" v-if="contractDetail.id">
          <span class="w-[calc(50%-6px)]">签约客户: {{ contractDetail.customer_dictText }}</span>
          <span class="w-[calc(50%-6px)]">签约日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">管理公司: {{ contractDetail.manageCompany_dictText }}</span>
          <span class="w-[calc(50%-6px)]">合同类型: {{ contractDetail.contractType_dictText }}</span>
          <span class="w-[calc(50%-6px)]">业务员: {{ contractDetail.operator_dictText }}</span>
          <span class="w-[calc(50%-6px)]">租赁单元: {{ contractDetail.leaseUnit }}</span>
          <span class="w-[calc(50%-6px)]">合同开始日期: {{ contractDetail.startDate }}</span>
          <span class="w-[calc(50%-6px)]">合同结束日期: {{ contractDetail.expireDate }}</span>
        </div>
      </a-form>
      <section v-if="form.fileFillType">
        <h4 class="text-[16px] font-bold mt-[40px] mb-[16px]">归档材料明细</h4>
        <a-table :data-source="form.dataFileFillDetailEntryList" :columns="columns" :pagination="false" bordered>
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'index'">{{ index + 1 }}</template>
            <template v-if="column.dataIndex === 'materialType'">
              <dict-select v-model="record.materialType" code="CT_BAS_MaterialType"></dict-select>
            </template>
            <template v-if="column.dataIndex === 'submitPerson'">
              <user-select v-model="record.submitPerson"></user-select>
            </template>
            <template v-if="column.dataIndex === 'receivePerson'">
              <user-select v-model="record.receivePerson"></user-select>
            </template>
            <template v-if="column.dataIndex === 'fileFillDate'">
              <a-date-picker v-model:value="record.fileFillDate" value-format="YYYY-MM-DD"></a-date-picker>
            </template>
          </template>
        </a-table>
      </section>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button type="primary" :loading="saveLoading" ghost @click="handleSave">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { edit, submit, detail, save, fileDetail } from '../apis.js'
import { page, queryTypeList } from '@/views/contract/archiveType/apis'
import { detail as getContractDetail, queryContractLeaseUnits } from '@/views/contract/management/apis'
import { message } from 'ant-design-vue'
import ContractSelect from '@/views/contract/management/components/ContractSelect.vue'
import dayjs from 'dayjs'

const emit = defineEmits(['refresh'])

const getTypeList = () => page({ pageNo: 1, pageSize: 100000, status: 'ENABLE' })

const visible = ref(false)

const open = (id) => {
  visible.value = true
  if (id) {
    loadDetail(id)
  } else {
    form.bizDate = dayjs(Date.now()).format('YYYY-MM-DD')
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await detail({ id })
  for (const key in form) {
    form[key] = result[key]
  }
  const { result: list } = await fileDetail({ id })
  form.dataFileFillDetailEntryList = list
  if (result.contract) {
    loadContractDetail(result.contract)
  }
  loading.value = false
}

const form = reactive({
  id: '',
  number: '',
  bizDate: '',
  fileFillDepart: '',
  fileFillType: '',
  status: '',
  contract: '',
  contractNumber: '',
  manageCompany: '',
  signDate: '',
  customer: '',
  contractType: '',
  operator: '',
  operatorDepart: '',
  startDate: '',
  expireDate: '',
  leaseUnit: '',
  remark: '',
  dataFileFillDetailEntryList: []
})

const rules = {
  bizDate: [{ required: true, message: '请选择业务时间', trigger: 'change' }],
  fileFillDepart: [{ required: true, message: '请选择归档部门', trigger: 'change' }],
  fileFillType: [{ required: true, message: '请选择归档类型', trigger: 'change' }],
  contract: [{ required: true, message: '请选择合同', trigger: 'change' }]
}

const contractDetail = reactive({})
const loadContractDetail = async (id) => {
  if (!id) {
    contractDetail.id = ''
    return
  }
  const { result } = await getContractDetail({ id })
  Object.assign(contractDetail, result)
  form.contract = id
  form.signDate = result.signDate
  form.customer = result.customer
  form.contractType = result.contractType
  form.contractNumber = result.contractNumber
  form.manageCompany = result.manageCompany
  form.operator = result.operator
  form.operatorDepart = result.operatorDepart
  form.startDate = result.startDate
  form.expireDate = result.expireDate
  const { result: list } = await queryContractLeaseUnits({ id })
  form.leaseUnit = list.map((item) => item.leaseUnit).join(',')
  contractDetail.leaseUnit = list.map((item) => item.leaseUnit_dictText).join(',')
}

const handleTypeChange = async (id) => {
  const { result } = await queryTypeList({ id })
  form.dataFileFillDetailEntryList = result.map((item) => {
    item.id = ''
    item.fileFillDate = dayjs(Date.now()).format('YYYY-MM-DD') // 归档日期
    return item
  })
}

const columns = [
  { title: '序号', dataIndex: 'index', width: 80, fixed: 'left' },
  { title: '材料类别', dataIndex: 'materialType' },
  { title: '提交人', dataIndex: 'submitPerson' },
  { title: '接收人', dataIndex: 'receivePerson' },
  { title: '归档日期', dataIndex: 'fileFillDate' }
]

// 暂存
const saveLoading = ref(false)
const handleSave = async () => {
  if (saveLoading.value) return
  try {
    saveLoading.value = true
    form.id ? await edit(getParams()) : await save(getParams())
    message.success('已暂存')
    emit('refresh')
  } finally {
    saveLoading.value = false
  }
}

const getParams = () => {
  return {
    id: form.id,
    number: form.number,
    bizDate: form.bizDate,
    fileFillDepart: form.fileFillDepart,
    fileFillType: form.fileFillType,
    status: form.status,
    contract: form.contract,
    signDate: form.signDate,
    customer: form.customer,
    manageCompany: form.manageCompany,
    contractType: form.contractType,
    operator: form.operator,
    operatorDepart: form.operatorDepart,
    startDate: form.startDate,
    expireDate: form.expireDate,
    leaseUnit: form.leaseUnit,
    remark: form.remark,
    dataFileFillDetailEntryList: form.dataFileFillDetailEntryList
  }
}

// 校验归档材料明细是否正确填写完整
const validateList = () => {
  const illegal = form.dataFileFillDetailEntryList.some((item, index) => {
    if (!item.materialType) {
      message.warning(`第${index + 1}条归档材料，请选择材料类别`)
      return true
    }
    if (!item.submitPerson) {
      message.warning(`第${index + 1}条归档材料，请选择提交人`)
      return true
    }
    if (!item.receivePerson) {
      message.warning(`第${index + 1}条归档材料，请选择接收人`)
      return true
    }
    if (!item.fileFillDate) {
      message.warning(`第${index + 1}条归档材料，请选择归档日期`)
      return true
    }
    return false
  })
  return !illegal
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  if (!validateList()) return
  try {
    confirmLoading.value = true
    await submit(getParams())
    confirmLoading.value = false
    handleCancel()
    message.success('提交成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const handleCancel = () => {
  for (const key in form) {
    if (Array.isArray(form[key])) {
      form[key] = []
    } else {
      form[key] = ''
    }
  }
  contractDetail.id = ''
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-file-archiving-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 40px;
  }
  .ant-form-item {
    width: calc(50% - 20px);
  }
  .form-item-full {
    width: 100%;
  }
}
</style>

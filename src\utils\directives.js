import { message } from 'ant-design-vue'
import useUserStore from '@/store/modules/user'

export const permission = {
  mounted(el, binding) {
    const { permission } = useUserStore()
    const codeList = permission.value.codeList
    const hasPermission = codeList && codeList.length && codeList.includes(binding.value)

    if (!hasPermission) {
      // 克隆原元素用于替换，防止其他副作用
      const newEl = el.cloneNode(true)

      // 禁用原按钮行为
      newEl.addEventListener('click', (e) => {
        e.stopImmediatePropagation() // 阻止所有事件处理器
        e.preventDefault()
        message.warn('您没有执行此操作的权限，请联系管理员')
      })

      // 替换原元素（可以防止 setup 中的绑定的点击事件继续工作）
      el.replaceWith(newEl)
    }
  }
}

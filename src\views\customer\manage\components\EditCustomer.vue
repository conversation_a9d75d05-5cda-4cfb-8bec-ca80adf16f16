<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}客户`"
    class="common-drawer edit-customer-drawer"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <circle-steps
        :current="currentStep + 1"
        :step-list="stepList"
        width="900px"
        class="mx-auto mb-[40px]"
      ></circle-steps>

      <!-- 基础信息及需求 -->
      <basic-info-step
        v-show="currentStep === 0"
        ref="basicStepRef"
        :form-data="formData"
        :loading="confirmLoading"
        @update:loading="confirmLoading = $event"
      />

      <!-- 联系信息 -->
      <contact-info-step v-show="currentStep === 1" ref="contactStepRef" :form-data="formData" />

      <!-- 财务信息 -->
      <finance-info-step v-show="currentStep === 2" ref="financeStepRef" :form-data="formData" />
    </a-spin>

    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSave">完成并启用</a-button>
      <a-button :disabled="currentStep <= 0" @click="handlePrevStep">上一步</a-button>
      <a-button :disabled="currentStep >= stepList.length - 1" @click="handleNextStep">下一步</a-button>
      <a-button :loading="confirmLoading" @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addCustomer, editCustomer } from '../apis'
import BasicInfoStep from './BasicInfoStep.vue'
import ContactInfoStep from './ContactInfoStep.vue'
import FinanceInfoStep from './FinanceInfoStep.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const currentStep = ref(0)

const basicStepRef = ref()
const contactStepRef = ref()
const financeStepRef = ref()

const stepList = ['基础信息及需求', '联系信息', '财务信息']

// 表单默认值
const formDataDefault = {
  id: undefined,
  name: undefined,
  isInternalCompany: false,
  internalCompany: undefined,
  manageCompany: undefined,
  customerSource: undefined,
  customerType: undefined,
  busiLicence: undefined,
  busiLicenceNum: undefined,
  legalPersonName: undefined,
  legalPerson: undefined,
  registeredPcaCode: undefined,
  registeredPcaCodeArray: [],
  registeredAddress: undefined,
  performance: undefined,
  safeRate: undefined,
  maintainDate: undefined,
  maintainPerson: undefined,
  initRequire: undefined,
  linkman: undefined,
  linkPcaCode: undefined,
  linkPcaCodeArray: [],
  linkDetailAddress: undefined,
  linkmanPhone: undefined,
  pushMobile: undefined,
  email: undefined,
  depositBank: undefined,
  depositBankAccount: undefined,
  invoiceName: undefined,
  invoiceType: undefined,
  taxRate: undefined
}
const formData = reactive({ ...formDataDefault })

/**
 * 打开编辑抽屉
 */
const open = (record) => {
  if (record && record.id) {
    Object.assign(formData, record)

    // 如果有 registeredPcaCode，则拆分为数组
    if (formData.registeredPcaCode && typeof formData.registeredPcaCode === 'string') {
      formData.registeredPcaCodeArray = formData.registeredPcaCode.split(',')
    }

    // 如果有 linkPcaCode，则拆分为数组
    if (formData.linkPcaCode && typeof formData.linkPcaCode === 'string') {
      formData.linkPcaCodeArray = formData.linkPcaCode.split(',')
    }
  }

  visible.value = true
}

const handleNextStep = async () => {
  if (currentStep.value >= stepList.length - 1) return
  try {
    switch (currentStep.value) {
      case 0:
        await basicStepRef.value?.validate()
        break
      case 1:
        await contactStepRef.value?.validate()
        break
    }
    currentStep.value++
  } catch {
    message.error('请填写完必填项后再进入下一步')
  }
}

const handlePrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    // 验证所有表单
    await Promise.all([
      basicStepRef.value?.validate(),
      contactStepRef.value?.validate(),
      financeStepRef.value?.validate()
    ])
  } catch {
    message.error('请检查并确认表单信息完整、正确')
    confirmLoading.value = false
    return
  }

  // 保存并启用
  if (!isTemporary) {
    formData.status = 'ENABLE'
  }
  // 根据操作类型选择对应的API
  const api = formData.id ? editCustomer : addCustomer

  try {
    formData.registeredPcaCode = formData.registeredPcaCodeArray.join(',')
    formData.linkPcaCode = formData.linkPcaCodeArray.join(',')
    await api(formData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`客户信息${action}成功`)
  } finally {
    confirmLoading.value = false
  }
  handleCancel()
}

/**
 * 保存客户信息
 */
const handleSave = () => saveData(false)

/**
 * 暂存客户信息
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 取消编辑并重置表单
 */
const handleCancel = () => {
  currentStep.value = 0
  basicStepRef.value?.resetFields()
  contactStepRef.value?.resetFields()
  financeStepRef.value?.resetFields()

  Object.assign(formData, formDataDefault)

  emits('refresh')

  visible.value = false
}

defineExpose({ open })
</script>

<style lang="less">
.edit-customer-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }
  .ant-form-item {
    width: calc(50% - 10px);
  }
  .form-item-full {
    width: 100%;
  }
  .ant-picker {
    width: 100%;
  }
  .ant-form-item-control {
    display: flex;
  }
}
</style>

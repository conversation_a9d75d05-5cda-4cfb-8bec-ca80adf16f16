<template>
  <a-drawer
    v-model:open="visible"
    :mask-closable="false"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === 0 }"
            @click="handleSwitch(1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === ids.length - 1 }"
            @click="handleSwitch()"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span v-if="detailData.status !== 'AUDITING'" class="primary-btn" @click="handleEdit">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn" @click="viewCheckRecord">查看审核记录</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="Reverse">反审核</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn" @click="handleDel">删除</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">资产处置详情</h2>
        <a-tag :type="getStatusColor(detailData.status)">
          {{ detailData.status_dictText }}
        </a-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>
      <!-- 锚点导航 -->
      <anchor-tabs :tab-list="navList" height="calc(100vh - 295px)">
        <!-- 基础信息 -->
        <template #baseInfo>
          <h2 class="text-[#1D335C] text-[16px] font-bold mb-[12px]">资产处置基础信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
            <span class="w-[50%]">业务日期：{{ detailData.bizDate || '-' }}</span>
            <span class="w-[50%]">
              资产来源：{{ renderDict(detailData.assetsSource, 'CT_BAS_AssetsSource') || '-' }}
            </span>
            <span class="w-[50%]">处置说明：{{ detailData.dealExplain || '-' }}</span>
            <span class="w-[50%]">房产处置日期：{{ detailData.houseDealDate || '-' }}</span>
            <span class="w-[50%]">转让方式：{{ detailData.transferMethod_dictText || '-' }}</span>
            <span class="w-[50%]">转让方：{{ detailData.transferor_dictText || '-' }}</span>
            <span class="w-[50%]">联合转让：{{ detailData.isUnion ? '是' : '否' || '-' }}</span>
            <span class="w-[50%]">内部决策情况：{{ detailData.decision_dictText || '-' }}</span>
            <span class="w-[50%]">披露内容描述：{{ detailData.discloseDesc || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">处置资产</h2>
          <a-table
            :data-source="detailData.houseDealBillEntryList"
            :columns="columns"
            :pagination="false"
            :scroll="{ y: 300, x: 1300 }"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <span class="primary-btn" @click="rowViewAssetsDetail(record)">资产详情</span>
              </template>
            </template>
          </a-table>
        </template>
        <!-- 处置过程 -->
        <template #disposalProcess>
          <h2 class="text-[#1D335C] text-[16px] font-bold mb-[12px]">集团批复</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">请示文号：{{ detailData.reqReferNum || '-' }}</span>
            <span class="w-[50%]">批复文号：{{ detailData.replyReferNum || '-' }}</span>
            <span class="w-[50%]">批复日期：{{ detailData.replyDate || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">评估正式稿</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">评估报告编号：{{ detailData.assessReportNum || '-' }}</span>
            <span class="w-[50%]">评估机构名称：{{ detailData.assessOrg || '-' }}</span>
            <span class="w-[50%]">评估方式：{{ detailData.assessWayBase_dictText || '-' }}</span>
            <span class="w-[50%]">评估师编号：{{ detailData.appraiserNum || '-' }}</span>
            <span class="w-[50%]">评估师名称：{{ detailData.apprasierName || '-' }}</span>
            <span class="w-[50%]">评估价值：{{ detailData.assessVal || '-' }}</span>
            <span class="w-[50%]">评估基准日：{{ detailData.assessStandardDate || '-' }}</span>
            <span class="w-[50%]">账面价值：{{ detailData.paperVal || '-' }}</span>
            <span class="w-[50%]">增减值：{{ detailData.addSubVal || '-' }}</span>
            <span class="w-[50%]">增值率：{{ detailData.addRate || '-' }}</span>
            <span class="w-[50%]">评估正式稿出具日期：{{ detailData.assessReportDate || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">产权交易中心挂牌</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">转让底价：{{ detailData.transferBaseVal || '-' }}</span>
            <span class="w-[50%]">保证金：{{ detailData.assureCash || '-' }}</span>
            <span class="w-[50%]">最高加价幅度：{{ detailData.maxUpRange || '-' }}</span>
            <span class="w-[50%]">承租人是否行使优先权：{{ detailData.renterIsUsePriority ? '是' : '否' || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">拍卖行成交确认书</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">成交价：{{ detailData.dealCloseVal || '-' }}</span>
            <span class="w-[50%]">成交人名称：{{ detailData.dealCloseName || '-' }}</span>
            <span class="w-[50%]">身份证号：{{ detailData.dealCloseIdCardNum || '-' }}</span>
            <span class="w-[50%]">成交人电话：{{ detailData.dealClosePhone || '-' }}</span>
            <span class="w-[50%]">成交日期：{{ detailData.dealCloseDate || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">产权交易中心鉴证书</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">出具日期：{{ detailData.provideAuthBookDate || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">移交房产</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">移交日期：{{ detailData.turnHouseDate || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">划拨转出让</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">划拨转出让日期：{{ detailData.transferOutDate || '-' }}</span>
          </div>
          <h2 class="text-[#1D335C] text-[16px] font-bold mt-[12px] mb-[12px]">网签过户</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">网签过户日期：{{ detailData.netSignDate || '-' }}</span>
          </div>
        </template>
        <!-- 资产评估 -->
        <template #assetValuation>
          <h2 class="text-[#1D335C] text-[16px] font-bold mb-[12px]">资产评估</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">评估机构：{{ detailData.assessOrganizati_dictText || '-' }}</span>
            <span class="w-[50%]">评估核准(备案)机构：{{ detailData.approveOrganizat_dictText || '-' }}</span>
            <span class="w-[50%]">核准(备案)日期：{{ detailData.approveDate || '-' }}</span>
            <span class="w-[50%]">评估基准日：{{ detailData.assessDate || '-' }}</span>
            <span class="w-[50%]">评估报告文号：{{ detailData.assessReportNo || '-' }}</span>
            <span class="w-[50%]">评估基准日审计机构：{{ detailData.auditOrganizatio_dictText || '-' }}</span>
            <span class="w-[50%]">律师事务所：{{ detailData.lawOffice_dictText || '-' }}</span>
            <span class="w-[50%]">标的评估值：{{ detailData.appraisePrice || '-' }}</span>
            <span class="w-[50%]">转让标的评估总值：{{ detailData.appraiseTotalPrice || '-' }}</span>
          </div>
        </template>
        <!-- 披露信息 -->
        <template #disclosureInfo>
          <h2 class="text-[#1D335C] text-[16px] font-bold mb-[12px]">披露信息</h2>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">公示开始日期：{{ detailData.publicityStartDate || '-' }}</span>
            <span class="w-[50%]">公示截止日期：{{ detailData.publicityEndDate || '-' }}</span>
            <span class="w-[50%]">内容描述：{{ detailData.contentDesc || '-' }}</span>
          </div>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <add-edit ref="addEditRef" @load-data="getDetailById(detailData.id)"></add-edit>
  <!-- 详情 -->
  <detail ref="detailRef" :ids="tableIds || []"></detail>
</template>
<script setup>
import Detail from '@/views/assets/manage/components/Detail.vue'
import AddEdit from './AddEdit.vue'
import { Modal, message } from 'ant-design-vue'
import { detailById, queryHouseDealBillEntryByMainId, delById, unAudit } from '../apis'
import { renderBoolean, renderDict } from '@/utils/render'
const emits = defineEmits(['loadData'])
const { ids } = defineProps({
  ids: { type: Array, required: true }
})
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    const activeIndex = ids.indexOf(data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    // detailData.value = data
    getDetailById(data.id)
  }
}
defineExpose({ open })
// 通过id获取详情
const getDetailById = async (id) => {
  const { result } = await detailById(id)
  const data = await queryHouseDealBillEntryByMainId(id)
  result.houseDealBillEntryList = data.result
  detailData.value = result
}
const tableIds = computed(() => {
  return detailData.value.houseDealBillEntryList.map((item) => item.houseOwner)
})
// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    AUDITOK: 'success', // 审核通过
    AUDITING: 'processing', // 审核中
    TEMP: 'default', // 暂存
    AUDITNO: 'error', // 审核不通过
    BACK: 'warning', // 已撤回
    ENABLE: 'success', // 启用
    DISABLE: 'error', // 禁用
    CLOSED: 'default', // 关闭
    INTEND: 'blue' // 意向
  }
  return statusMap[status] || 'default'
}
const columns = [
  { title: '资产名称', dataIndex: 'houseOwner_dictText', width: 150, ellipsis: true, fixed: true },
  { title: '地址', dataIndex: 'detailAddress', ellipsis: true },
  { title: '转让面积(㎡)', dataIndex: 'transferArea' },
  { title: '评估价值(万元)', dataIndex: 'evaluatePrice' },
  { title: '受让方', dataIndex: 'transferee' },
  { title: '处置说明', dataIndex: 'dealExplain' },
  { title: '是否成交', dataIndex: 'deal', customRender: ({ text }) => renderBoolean(text) },
  { title: '成交面积', dataIndex: 'dealArea' },
  { title: '成交价（万元）', dataIndex: 'dealPrice' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]
// 导航项
const navList = [
  { name: 'baseInfo', title: '基础信息', showTitle: false },
  { name: 'disposalProcess', title: '处置过程', showTitle: false },
  { name: 'assetValuation', title: '资产评估', showTitle: false },
  { name: 'disclosureInfo', title: '披露信息', showTitle: false }
]

const detailData = ref({
  houseDealBillEntryList: []
})
const loading = ref(false)

const handleSwitch = (type) => {
  // 上一条
  if (type) {
    if (curIndex.value > 0) {
      curIndex.value--
      getDetailById(ids[curIndex.value])
    }
    return
  }
  if (curIndex.value < ids.length - 1) {
    curIndex.value++
    getDetailById(ids[curIndex.value])
  }
}
// 编辑
const addEditRef = ref()
const handleEdit = () => {
  addEditRef?.value.open(detailData.value)
  handleClose()
}
// 查看审核记录
const viewCheckRecord = () => {}
// 反审核
const Reverse = () => {
  Modal.confirm({
    title: '确认反审核？',
    content: '',
    centered: true,
    onOk: async () => {
      await unAudit({ id: detailData.value.id })
      message.success('反审核成功')
      emits('loadData')
      handleClose()
    }
  })
}
// 删除
const handleDel = () => {
  Modal.confirm({
    title: '确认删除当前资产处置？',
    content: '',
    centered: true,
    onOk: async () => {
      await delById(detailData.value.id)
      message.success('删除成功')
      emits('loadData')
      handleClose()
    }
  })
}

// 查看资产详情
const detailRef = ref()
const rowViewAssetsDetail = (row) => {
  detailRef?.value.open(row.houseOwner)
}

// 关闭弹窗
const handleClose = () => {
  curIndex.value = 0
  visible.value = false
}
</script>

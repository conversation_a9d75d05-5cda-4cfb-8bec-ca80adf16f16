<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button v-show="selectedRowKeys.length" @click="handleRemove(false)">批量删除</a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.contractNumber"
          placeholder="搜索合同编号"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleAudit(record, true)">审核(临时功能)</a-menu-item>
                <a-menu-item @click="handleWithdraw(record)" v-if="['AUDITING'].includes(record.status)">
                  撤回
                </a-menu-item>
                <a-menu-item @click="handleEdit(record)" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  编辑
                </a-menu-item>
                <a-menu-item
                  @click="handleTermination(record)"
                  v-if="['InExecution', 'Modified'].includes(record.bizStatus)"
                >
                  中止
                </a-menu-item>
                <a-menu-item @click="handleCancelTermination(record)" v-if="['Suspended'].includes(record.bizStatus)">
                  取消中止
                </a-menu-item>
                <a-menu-item @click="handleRemove(record)" v-if="['TEMP', 'BACK'].includes(record.status)">
                  删除
                </a-menu-item>
                <a-menu-item
                  @click="handleChange(record)"
                  v-if="['InExecution', 'Modified'].includes(record.bizStatus)"
                >
                  变更
                </a-menu-item>
                <a-menu-item
                  @click="handleRenew(record)"
                  v-if="!['NotEffective', null, undefined, ''].includes(record.bizStatus)"
                >
                  续签
                </a-menu-item>
                <a-menu-item @click="handleAdjust(record)" v-if="['InExecution'].includes(record.bizStatus)">
                  账单调整
                </a-menu-item>
                <a-menu-item @click="handleClearing(record)" v-if="['Suspended'].includes(record.bizStatus)">
                  退租清算
                </a-menu-item>
                <a-menu-item
                  @click="handleAudit(record, false)"
                  v-if="['InExecution', 'Modified', 'Suspended', 'Cleared'].includes(record.bizStatus)"
                >
                  反审核
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit-contract ref="editContractRef" @refresh="refresh"></edit-contract>
    <contract-detail
      ref="contractDetailRef"
      :data-list="list"
      @refresh="refreshFromDetail"
      @edit="handleEdit"
      @abort="handleTermination"
      @cancel-abort="handleCancelTermination"
      @change="handleChange"
      @renew="handleRenew"
      @withdraw="handleWithdraw"
    ></contract-detail>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('合同导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <change-reason ref="changeReasonRef" @confirm-change="confirmContractChange"></change-reason>
    <contract-termination ref="contractTerminationRef" @refresh="refreshFromTermination"></contract-termination>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit, withdraw, cancelAbort } from './apis.js'
import EditContract from './components/EditContract.vue'
import ContractDetail from './components/ContractDetail.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag } from '@/utils/render'
import ChangeReason from './components/ChangeReason.vue'
import ContractTermination from './components/ContractTermination.vue'

const route = useRoute()
const router = useRouter()

const pageTitle = computed(() => route.meta.title)

const params = reactive({
  number: undefined,
  contractNumber: undefined,
  status: undefined,
  bizStatus: undefined,
  bizDate: undefined,
  signDate: undefined,
  customer: undefined,
  contractType: undefined,
  manageCompany: undefined,
  operator: undefined,
  operatorDepart: undefined,
  pricedType: undefined,
  startDate: undefined,
  expireDate: undefined,
  terminateDate: undefined,
  terminateReason: undefined,
  changeReason: undefined,
  originalContract: undefined,
  totalArea: undefined,
  totalRental: undefined,
  totalRemission: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  attachmentIds: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined
})

const searchList = [
  { label: '编号', name: 'number', type: 's-input' },
  { label: '审核人', name: 'auditBy', type: 'user-select' },
  { label: '业务状态', name: 'bizStatus', type: 'dict-select', code: 'CT_BASE_ENUM_Contract_BizStatus' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '合同类型', name: 'contractType', type: 'dict-select', code: 'CT_BAS_ContractType' },
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '签约日期', name: 'signDate', type: 'date' },
  { label: '合同开始时间', name: 'startDate', type: 'date' },
  { label: '合同结束时间', name: 'expireDate', type: 'date' },
  { label: '业务员', name: 'operator', type: 'user-select' },
  { label: '业务部门', name: 'operatorDepart', type: 'dept-tree-select' },
  { label: '定价类型', name: 'pricedType', type: 'dict-select', code: 'CT_BASE_ENUM_Contract_PricedType' },
  { label: '物业管理公司', name: 'manageCompany', type: 'company-select' },
  { label: '客户', name: 'customer', type: 'customer-select' }
]

const defaultColumns = [
  { title: '合同编号', dataIndex: 'contractNumber', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 120 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_Contract_BizStatus', 'dot')
  },
  { title: '物业管理公司', dataIndex: 'manageCompany_dictText', width: 160 },
  { title: '业务日期', dataIndex: 'remark', width: 100 },
  { title: '签约日期', dataIndex: 'signDate', width: 120 },
  { title: '业务人员', dataIndex: 'operator_dictText', width: 140 },
  { title: '合同类型', dataIndex: 'contractType_dictText', width: 130 },
  {
    title: '租金(元/月)',
    dataIndex: 'amountPerMonth',
    width: 140,
    customRender: ({ text }) => (text ? `${text}元/月` : '')
  },
  { title: '开始日期', dataIndex: 'startDate', width: 120 },
  { title: '结束日期', dataIndex: 'expireDate', width: 120 },
  { title: '操作', dataIndex: 'action', width: 140, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editContractRef = ref()
const handleAdd = () => {
  editContractRef.value.open()
}
const handleEdit = (data) => {
  editContractRef.value.open(data.id)
}

const contractDetailRef = ref()
const handleView = (data) => {
  contractDetailRef.value.open(data.id)
}

const contractTerminationRef = ref()
const handleTermination = (data) => {
  contractTerminationRef.value.open(data)
}

const handleCancelTermination = (data) => {
  Modal.confirm({
    title: '确认取消中止？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await cancelAbort({ id: data.id, bizStatus: data.bizStatus })
      message.success('已取消中止')
      onTableChange(pagination.value)
      if (contractDetailRef.value && contractDetailRef.value.visible) {
        contractDetailRef.value.loadData(data.id)
      }
    }
  })
}
const handleRenew = (data) => {
  editContractRef.value.open(data.id, 'renew')
}

const handleWithdraw = (data) => {
  Modal.confirm({
    title: '是否确认撤回该合同申请？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await withdraw({ id: data.id })
      message.success('已撤回')
      onTableChange(pagination.value)
      if (contractDetailRef.value && contractDetailRef.value.visible) {
        contractDetailRef.value.loadData(data.id)
      }
    }
  })
}

const changeReasonRef = ref()
// 合同变更
const handleChange = (data) => {
  changeReasonRef.value.open(data.id)
}

/**
 * 确认合同变更
 * @param {Object} form { originalContract: 原合同id, changeReason: 变更原因 }
 */
const confirmContractChange = (form) => {
  editContractRef.value.open(form.originalContract, 'change', form)
}

const handleAdjust = (data) => {
  sessionStorage.setItem('contractId', data.id)
  router.push({ path: '/contract/billAdjust' })
}
const handleClearing = (data) => {
  sessionStorage.setItem('contractId', data.id)
  router.push({ path: '/contract/clearing' })
}

const handleAudit = async (data, result) => {
  result ? await audit({ id: data.id }) : await unAudit({ id: data.id })
  message.success('保存成功')
  onTableChange(pagination.value)
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除合同？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

const refreshFromTermination = (contractId) => {
  onTableChange(pagination.value)
  if (contractDetailRef.value && contractDetailRef.value.visible) {
    contractDetailRef.value.loadData(contractId)
  }
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('招租方案数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>

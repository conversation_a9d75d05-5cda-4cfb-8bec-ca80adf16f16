<template>
  <div>
    <h2 class="text-[18px] font-bold mb-[34px]">{{ pageTitle }}</h2>

    <div class="flex border border-[#E6E9F0] rounded-[8px]">
      <div class="!w-[240px] p-[20px] border-r-1 border-r-[#E6E9F0]">
        <div class="flex justify-between pb-[20px]">
          <h2 class="font-bold text-[18px]">服务类型</h2>
          <div>
            <span class="a-icon-plus text-[18px] font-bold text-primary"></span>
            <span>添加类型</span>
          </div>
        </div>
        <draggable v-model="tabList" handle=".a-icon-move" item-key="id">
          <template #item="{ element }">
            <div class="flex text-[14px] pt-[10px] pb-[10px] pr-[10px]">
              <i class="a-icon-move cursor-move text-secondary mr-[10px]"></i>
              <div>{{ element.name }}</div>
            </div>
          </template>
        </draggable>
      </div>

      <div class="!w-[calc(100%-240px)] m-[20px]">
        <h2 class="font-bold text-[18px]">计费项</h2>
        <div class="flex justify-between !my-[24px]">
          <div class="flex">
            <a-button type="primary" @click="handleAdd">
              <span class="a-icon-plus mr-[8px]"></span>
              新建
            </a-button>
            <a-button @click="handleImport">
              <span class="a-icon-import-right mr-[8px]"></span>
              导入
            </a-button>
            <a-button :loading="exportLoading" @click="handleExport">
              <span class="a-icon-export-right mr-[8px]"></span>
              导出
            </a-button>
            <a-dropdown v-if="selectedRowKeys.length">
              <a-button>
                <span>批量操作</span>
                <i class="a-icon-arrow-down text-[12px] ml-[8px]"></i>
              </a-button>
              <template #overlay>
                <a-menu>
                  <a-menu-item>
                    <span class="primary-btn">删除</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
            <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
              <i class="a-icon-refresh"></i>
              刷新
            </a-button>
            <a-form autocomplete="off" layout="inline" class="!ml-[40px]">
              <a-form-item label="搜索">
                <s-input
                  v-model="search.number"
                  placeholder="搜索名称"
                  class="ml-[10px] !w-[280px]"
                  @input="handleInput"
                ></s-input>
              </a-form-item>
              <a-form-item>
                <search-more v-model="search" :search-list="searchList" @searchChange="onTableChange"></search-more>
              </a-form-item>
            </a-form>
          </div>
          <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
        </div>
        <a-table
          :data-source="[{ id: '2222', name: '测试计费项' }]"
          :columns="columns"
          :loading="tableLoading"
          :scroll="{ y: tableHeight, x: 1000 }"
          :pagination="pagination"
          row-key="id"
          :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
          @change="onTableChange"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <span class="primary-btn" @click="rowEdit(record)">编辑</span>
              <span class="primary-btn" @click="rowEdit(record)">删除</span>
            </template>
          </template>
        </a-table>
      </div>
    </div>
    <add-edit ref="addEditRef"></add-edit>
    <!-- 导入 -->
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入资产处置单"
      :download-fn="() => exportExcel('资产处置单数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
  </div>
</template>
<script setup>
import draggable from 'vuedraggable'
import { getPage, exportExcel, importExcel } from './apis'
import AddEdit from './components/AddEdit.vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { message } from 'ant-design-vue'
const route = useRoute()
const pageTitle = computed(() => route.meta.title)
const tabList = [
  { id: '1', name: '临时场地租赁' },
  { id: '2', name: '临时停车费租赁' },
  { id: '3', name: '商品销售' }
]
const search = ref({
  number: undefined
})
const searchList = reactive([])
let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}
const defaultColumns = [
  { title: '计费项编码', dataIndex: 'name', width: 150, fixed: true },
  { title: '计费项名称', dataIndex: 'name' },
  { title: '计费规则', dataIndex: 'name' },
  { title: '备注', dataIndex: 'name' },
  { title: '操作', dataIndex: 'action', width: 100, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)
const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(getPage)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id', true)
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...search.value })
}
// 新增
const addEditRef = ref()
const handleAdd = () => {
  addEditRef.value.open()
}
// 编辑
const rowEdit = (row) => {
  addEditRef?.value.open(row)
}
// 导入
const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}
// 导出
const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('楼栋数据导出.xls', search)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}
</script>

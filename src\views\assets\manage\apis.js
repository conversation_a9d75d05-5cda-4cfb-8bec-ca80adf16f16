import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
// 资产分页数据
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/bas/houseOwner/list',
    params
  })
}

// 资产f7分页数据
export const getF7List = (params) => {
  return request({
    method: 'get',
    url: '/bas/houseOwner/f7List',
    params
  })
}
// 资产提交
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/bas/houseOwner/submit',
    data
  })
}

// 资产暂存
export const stash = (data) => {
  return request({
    method: 'post',
    url: '/bas/houseOwner/add',
    data
  })
}
// 资产编辑
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/bas/houseOwner/edit',
    data
  })
}

// 资产分类
export const getQueryTreeList = () => {
  return request({
    method: 'get',
    url: '/bas/houseOwnerTree/queryTreeList'
  })
}

// 楼栋主表ID查询
export const getQueryWyBuildingByMainId = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyProject/queryWyBuildingByMainId',
    params
  })
}
// 楼层主表ID查询
export const getQueryWyFloorByMainId = (params) => {
  return request({
    method: 'get',
    url: '/bas/wyBuilding/queryWyFloorByMainId',
    params
  })
}

// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/bas/houseOwner/delete?id=${id}`
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/bas/houseOwner/deleteBatch?ids=${ids}`
  })
}
// 生成租赁单元
export const genLeaseUnit = (ids) => {
  return request({
    method: 'post',
    url: `/bas/houseOwner/genLeaseUnit?ids=${ids}`,
    data: {}
  })
}
// 资产-查看关联租赁单元
export const viewRelativeLeaseUnit = (ids) => {
  return request({
    method: 'get',
    url: `/bas/houseOwner/viewRelativeLeaseUnit?ids=${ids}`
  })
}

// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/bas/houseOwner/queryById?id=${id}`
  })
}
// 资产-启用禁用ids多个用，分开例如：1,2,3、status必填
export const updateEnableDisableStatus = (data) => {
  return request({
    method: 'post',
    url: '/bas/houseOwner/updateEnableDisableStatus',
    data
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/bas/houseOwner/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/bas/houseOwner/importExcel', data, controller)
}

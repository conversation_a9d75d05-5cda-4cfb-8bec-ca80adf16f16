<template>
  <a-modal
    v-model:open="visible"
    title="选择退款明细"
    width="1200px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleConfirm"
    @cancel="handleCancel"
  >
    <div class="flex mb-[16px]">
      <s-input
        v-model="searchParams.number"
        placeholder="搜索单据编号"
        class="!w-[280px]"
        @input="handleInput"
      ></s-input>
      <filter-more
        :params="searchParams"
        :search-list="searchList"
        width="320px"
        label-width="100px"
        @query="handleSearch"
      ></filter-more>
    </div>

    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        onChange: onSelectChange,
        type: 'checkbox'
      }"
      :scroll="{ y: '50vh', x: 1800 }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <status-tag :dict-value="record.status" dict-code="CT_BASE_ENUM_AuditStatus" type="dot"></status-tag>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { f7RefundDetailList } from '../apis'

const emits = defineEmits(['confirm'])

const visible = ref(false)
const preSelectedIds = ref([])
let timer

const searchParams = reactive({
  number: undefined,
  customer: undefined,
  contract: undefined,
  leaseUnit: undefined,
  paymentType: undefined,
  status: undefined,
  receiveDate: undefined
})

const searchList = [
  { label: '客户名称', name: 'customer', type: 's-input', placeholder: '请输入客户名称' },
  { label: '合同', name: 'contract', type: 's-input', placeholder: '请输入合同' },
  { label: '租赁单元', name: 'leaseUnit', type: 's-input', placeholder: '请输入租赁单元' },
  { label: '款项类型', name: 'paymentType', type: 's-input', placeholder: '请输入款项类型' },
  {
    label: '状态',
    name: 'status',
    type: 'dict-select',
    code: 'CT_BASE_ENUM_AuditStatus',
    placeholder: '请选择状态'
  },
  { label: '应收日期', name: 'receiveDate', type: 'date', placeholder: '请选择应收日期' }
]

const { list, pagination, tableLoading, onTableFetch } = usePageTable(f7RefundDetailList)
const { selectedRows, selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id', false)

/**
 * 弹窗表格列配置
 */
const columns = [
  { title: '单据ID', dataIndex: 'number', width: 200, fixed: 'left' },
  { title: '客户名称', dataIndex: 'customer_dictText', width: 160, ellipsis: true },
  { title: '合同', dataIndex: 'contract_dictText', width: 200, ellipsis: true },
  { title: '租赁单元', dataIndex: 'leaseUnit', width: 160, ellipsis: true },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '状态', dataIndex: 'status', width: 120 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '调整金额', dataIndex: 'receiveAmountAdjust', width: 120 },
  { title: '应收金额', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '剩余金额', dataIndex: 'residual', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '结清日期', dataIndex: 'receiveEndDate', width: 120 }
]

/**
 * 打开选择器
 * @param {Array} selectedDetailIds - 已选择的明细ID数组
 */
const open = (selectedDetailIds = []) => {
  visible.value = true
  // 清空之前的选择状态
  clearSelection()

  // 加载第一页数据
  handleSearch()

  // 如果有预选择的ID，需要在数据加载完成后设置
  if (selectedDetailIds.length > 0) {
    // 保存预选择的ID，用于后续设置
    preSelectedIds.value = [...selectedDetailIds]
  }
}

/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  preSelectedIds.value = []
  visible.value = false
}

/**
 * 确认选择
 */
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条明细记录')
    return
  }

  emits('confirm', selectedRows.value)
  handleCancel()
}

/**
 * 搜索输入处理
 */
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    handleSearch()
  }, 600)
}

/**
 * 处理搜索
 */
const handleSearch = () => {
  onTableFetch({ pageNo: 1, pageSize: pagination.value.pageSize, ...searchParams })
}

/**
 * 表格分页变化处理
 */
const onTableChange = ({ current, pageSize } = {}) => {
  const pageNo = current || 1
  const size = pageSize || pagination.value.pageSize
  onTableFetch({ pageNo, pageSize: size, ...searchParams })
}

/**
 * 监听表格数据变化，设置预选择的行
 */
watch(
  () => list.value,
  (newList) => {
    if (preSelectedIds.value.length > 0 && newList.length > 0) {
      nextTick(() => {
        // 设置选中的keys
        selectedRowKeys.value = [...preSelectedIds.value]
        // 找到当前页中匹配的数据行
        const currentPageSelectedRows = newList.filter((item) => preSelectedIds.value.includes(item.id))
        // 合并到已选择的行中（避免覆盖其他页面的选择）
        currentPageSelectedRows.forEach((row) => {
          const existIndex = selectedRows.value.findIndex((item) => item.id === row.id)
          if (existIndex === -1) {
            selectedRows.value.push(row)
          }
        })
      })
    }
  },
  { immediate: true }
)

defineExpose({
  open
})
</script>

<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex mt-[28px] mb-[16px]">
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus"></i>
        新建
      </a-button>
      <a-button @click="handleImport">
        <i class="a-icon-import-right"></i>
        导入
      </a-button>
      <a-button :loading="exportLoading" @click="handleExport">
        <i class="a-icon-export-right"></i>
        导出
      </a-button>
      <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
        <i class="a-icon-refresh"></i>
        刷新
      </a-button>
      <a-dropdown v-if="selectedRowKeys.length">
        <a-button>
          批量操作
          <i class="a-icon-arrow-down ml-[8px]"></i>
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="handleRemove(false)">删除</a-menu-item>
            <a-menu-item @click="handleStatusBatch('ENABLE')">启用</a-menu-item>
            <a-menu-item @click="handleStatusBatch('DISABLE')">禁用</a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
      <s-input
        v-model="params.name"
        placeholder="搜索楼栋名称"
        class="ml-[40px] !w-[280px]"
        @input="handleInput"
      ></s-input>
      <filter-more
        :params="params"
        label-width="100px"
        :search-list="searchList"
        @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
      ></filter-more>
      <div class="flex items-center ml-[10px]">
        <a-checkbox v-model:checked="viewEnabled">仅看启用</a-checkbox>
      </div>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ y: tableHeight }"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <span class="primary-btn" @click="handleEdit(record)">编辑</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleRemove(record)">删除</a-menu-item>
                <a-menu-item @click="handleImportFloor(record)">导入楼层</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
    </a-table>
    <edit-building ref="editBuildingRef" @refresh="refresh"></edit-building>
    <building-detail
      ref="buildingDetailRef"
      :data-list="list"
      @edit-building="handleEdit"
      @refresh="refreshFromDetail"
    ></building-detail>
    <common-import
      ref="commonImportRef"
      key="1"
      modal-title="批量导入楼栋"
      :download-fn="() => exportExcel('楼栋数据导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <common-import
      ref="importFloorRef"
      key="2"
      modal-title="批量导入楼层"
      :download-fn="() => exportFloorExcel('楼层数据导入模板.xls', { id: 0 })"
      :upload-fn="importFloorExcel"
    ></common-import>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { buildingPage, deleteBuilding, updateStatus, exportExcel, importExcel } from './apis/building.js'
import { exportExcel as exportFloorExcel, importExcel as importFloorExcel } from './apis/floor.js'
import EditBuilding from './components/EditBuilding.vue'
import BuildingDetail from './components/BuildingDetail.vue'
import { Modal, message } from 'ant-design-vue'
import { projectPage } from '@/views/projects/apis'

const route = useRoute()

const pageTitle = computed(() => route.meta.title)

const viewEnabled = ref(false) // 是否仅看启用

const params = reactive({
  id: undefined,
  name: undefined,
  number: undefined,
  wyProject: undefined,
  company: undefined,
  status: undefined,
  wyFloorCount: undefined,
  seq: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined,
  auditBy: undefined,
  auditTime: undefined,
  attachmentIds: undefined,
  sourceBillId: undefined,
  sourceBillEntryId: undefined,
  delFlag: undefined
})

const getProjectList = () => projectPage({ pageNo: 1, pageSize: 100000 })
const searchList = [
  { label: '楼栋编号', name: 'number', type: 's-input' },
  { label: '所属项目', name: 'wyProject', type: 'api-select', asyncFn: getProjectList },
  { label: '项目所属公司', name: 'company', type: 'company-select' },
  { label: '楼层数', name: 'wyFloorCount', type: 'input' },
  { label: '创建时间', name: 'createTime', type: 'date' },
  { label: '创建人', name: 'createBy', type: 'user-select' }
]

const columns = [
  { title: '楼栋名称', dataIndex: 'name', width: 150, fixed: 'left' },
  { title: '所属项目', dataIndex: 'wyProject_dictText' },
  { title: '项目所属公司', dataIndex: 'company_dictText' },
  { title: '启用状态', dataIndex: 'status' },
  { title: '楼层数', dataIndex: 'wyFloorCount' },
  { title: '备注', dataIndex: 'remark' },
  { title: '楼栋编号', dataIndex: 'number' },
  { title: '操作', dataIndex: 'action', width: 160, fixed: 'right' }
]

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(buildingPage, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
}

watch(viewEnabled, (val) => {
  params.status = val ? 'ENABLE' : undefined
  onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
})

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const { selectedRowKeys, onSelectChange, clearSelection } = useTableSelection(list, 'id')

const editBuildingRef = ref()
const handleAdd = () => {
  editBuildingRef.value.open()
}
const handleEdit = (data) => {
  editBuildingRef.value.open(data.id)
}

const buildingDetailRef = ref()
const handleView = (data) => {
  buildingDetailRef.value.open(data.id)
}

const importFloorRef = ref()
const handleImportFloor = () => {
  importFloorRef.value.open()
}

const handleStatusChange = async (data, val) => {
  if (data.loading) return
  try {
    data.loading = true
    await updateStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
    data.loading = false
    message.success('保存成功')
    data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    data.loading = false
    data.checked = !val
  }
}

/**
 * 删除楼栋
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除楼栋？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBuilding({ ids: data ? data.id : selectedRowKeys.value.join(',') })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (
        pageNo > 1 &&
        ((data && list.value.length === 1) || (!data && selectedRowKeys.value.length === list.value.length))
      ) {
        pageNo--
      }
      clearSelection()
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const handleStatusBatch = (status) => {
  Modal.confirm({
    title: `确认${status === 'ENABLE' ? '启用' : '禁用'}楼栋？`,
    content: status === 'ENABLE' ? '' : '楼栋禁用后将无法再被使用，但不影响已创建的数据。',
    centered: true,
    onOk: async () => {
      await updateStatus({ ids: selectedRowKeys.value.join(','), status })
      message.success(`已${status === 'ENABLE' ? '启用' : '禁用'}`)
      clearSelection()
      onTableChange(pagination.value)
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由楼栋详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('楼栋数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>

<template>
  <a-drawer
    v-model:open="visible"
    class="common-detail-drawer"
    placement="right"
    width="1072px"
    destroy-on-close
    @close="handleClose"
  >
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 }"
            @click="handleSwitch(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitch(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <a-dropdown>
          <div class="border-0 border-r border-solid border-[#E6E9F0] pr-[16px] text-primary cursor-pointer">
            <span>操作</span>
            <i class="a-icon-arrow-down ml-[4px]"></i>
          </div>
          <template #overlay>
            <a-menu>
              <a-menu-item @click="handleAudit(true)">审核(临时功能)</a-menu-item>
              <a-menu-item @click="handleViewContract">查看合同</a-menu-item>
              <a-menu-item @click="handleWithdraw" v-if="['AUDITING'].includes(detail.status)">撤回</a-menu-item>
              <a-menu-item @click="handleEdit" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detail.status)">
                编辑
              </a-menu-item>
              <a-menu-item @click="handleRemove" v-if="['TEMP', 'BACK'].includes(detail.status)">删除</a-menu-item>
              <a-menu-item
                @click="handleAudit(detail, false)"
                v-if="['InExecution', 'Modified', 'Suspended', 'Cleared'].includes(detail.bizStatus)"
              >
                反审核
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </div>
    </template>
    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">合同账单调整</h2>
        <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status"></status-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detail.number }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detail.createBy_dictText }} 提交于{{ detail.createTime }}</span>
      </div>
      <anchor-tabs :tab-list="tabList" height="calc(100vh - 284px)">
        <template #base>
          <div class="flex flex-wrap gap-y-[12px] text-secondary">
            <span class="w-[50%]">业务日期: {{ detail.bizDate }}</span>
            <span class="w-[50%]">合同编码: {{ detail.contract_dictText }}</span>
            <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
            <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
            <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
            <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
            <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
            <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
            <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
            <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
            <span class="w-[50%]">租赁单元: {{ detail.leaseUnit_dictText }}</span>
          </div>
        </template>
        <template #detail>
          <a-table :data-source="detail.billList" :columns="columns" :pagination="false" bordered :scroll="{ x: 1400 }">
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'addReduceAmount'">
                <span v-if="record.addReduceType === 'Add'" class="text-success">
                  {{ record.addReduceType_dictText }}{{ record.addReduceAmount }}
                </span>
                <span v-if="record.addReduceType === 'Reduce'" class="text-error">
                  {{ record.addReduceType_dictText }}{{ record.addReduceAmount }}
                </span>
              </template>
            </template>
          </a-table>
        </template>
        <template #attachment>
          <file-list :biz-id="detail.id"></file-list>
        </template>
        <template #result>
          <div
            class="border border-solid border-[#e0e0e0] p-[16px] rounded-[8px]"
            v-if="computedResult.title && detail.status !== 'TEMP'"
          >
            <div class="flex items-center justify-between">
              <strong class="text-[18px]">{{ computedResult.title }}</strong>
              <status-tag dict-code="CT_BASE_ENUM_AuditStatus" :dict-value="detail.status" type="dot"></status-tag>
            </div>
            <div class="mt-[16px] text-secondary">{{ computedResult.description }}</div>
          </div>
          <div v-if="detail.status === 'TEMP'" class="text-tertiary">暂存状态下没有处理进展</div>
        </template>
      </anchor-tabs>
    </a-spin>
  </a-drawer>
  <contract-detail ref="contractDetailRef" :data-list="[]"></contract-detail>
</template>

<script setup>
import { detail as getDetail, deleteBatch, adjustDetail, audit, unAudit } from '../apis.js'
import { Modal, message } from 'ant-design-vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'
import Decimal from 'decimal.js'

const { dataList } = defineProps({
  dataList: { required: true, type: Array }
})

const emit = defineEmits(['edit', 'refresh'])

const visible = ref(false)

const open = (id) => {
  visible.value = true
  loadData(id)
  currentIndex.value = dataList.findIndex((item) => item.id === id)
}

const currentIndex = ref(0)
const handleSwitch = (index) => {
  if (!dataList[index]) return
  currentIndex.value = index
  loadDetail(dataList[index].id)
}

const loading = ref(false)
const loadData = async (id) => {
  loading.value = true
  await Promise.all([loadDetail(id), loadBillList(id)])
  loading.value = false
}

const detail = reactive({
  id: '',
  actualDealAmount: '',
  bizDate: '',
  contract: '',
  contractType_dictText: '',
  contract_dictText: '',
  createBy_dictText: '',
  createTime: '',
  customer_dictText: '',
  expireDate: '',
  leaseUnit_dictText: '',
  manageCompany_dictText: '',
  number: '',
  operator_dictText: '',
  operatorDepart_dictText: '',
  signDate: '',
  startDate: '',
  status: '',
  billList: [] // 账单明细
})
const loadDetail = async (id) => {
  const { result } = await getDetail({ id })
  for (const key in detail) {
    if (key !== 'billList') {
      detail[key] = result[key]
    }
  }
}

const loadBillList = async (id) => {
  const { result } = await adjustDetail({ id })
  detail.billList = result
  handleCompute()
}

const tabList = [
  { title: '基础信息', name: 'base' },
  { title: '调整明细', name: 'detail' },
  { title: '附件信息', name: 'attachment' },
  { title: '处理进展', name: 'result' }
]

const columns = [
  { title: '账单ID', dataIndex: 'detailBillEntry', fixed: 'left' },
  { title: '款项类型', dataIndex: 'paymentType_dictText' },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod' },
  { title: '款项金额', dataIndex: 'paymentAmount' },
  { title: '已收金额', dataIndex: 'consumedAmount' },
  { title: '未收金额', dataIndex: 'noConsumedAmount' },
  { title: '应收日期', dataIndex: 'receiveDate' },
  { title: '增减金额', dataIndex: 'addReduceAmount' },
  { title: '调整原因', dataIndex: 'adjustRemark', ellipsis: true }
]

const computedResult = reactive({
  title: '', // 显示应退: xx元/调整后待核销合计: xx元
  description: '' // [累计款项金额] 6030.00 - [本次减免金额] 1600.00 + [本次增加金额] 520.00 - [累计已核销] 6500.00 = -1550.00
})
// 结算结果
const handleCompute = () => {
  if (!detail.billList.length) {
    computedResult.title = ''
    computedResult.description = ''
  }
  let total = new Decimal(0) // 累计款项金额
  let reduce = new Decimal(0) // 本次减免金额
  let add = new Decimal(0) // 本次增加金额
  let consume = new Decimal(0) // 累计已核销金额
  detail.billList.forEach((item) => {
    total = total.plus(new Decimal(item.paymentAmount))
    consume = consume.plus(new Decimal(item.consumedAmount))
    if (item.addReduceType === 'Add') {
      add = add.plus(new Decimal(Number(item.addReduceAmount)))
    } else {
      reduce = reduce.plus(new Decimal(Number(item.addReduceAmount)))
    }
  })
  const result = total.sub(reduce).plus(add).sub(consume).toNumber()
  computedResult.title = result < 0 ? `应退: ${result}` : `调整后待核销合计: ${result}`
  const totalStr = `[累计款项金额]${total.toString()}`
  const reduceStr = reduce.toNumber() > 0 ? ` - [本次减免金额]${reduce.toNumber()}` : ''
  const addStr = add.toNumber() > 0 ? ` + [本次增加金额]${add.toNumber()}` : ''
  const consumeStr = ` - [累计已核销]${consume.toNumber()}`
  computedResult.description = `${totalStr + reduceStr + addStr + consumeStr} = ${result}`
}

const handleEdit = () => {
  visible.value = false
  emit('edit', detail)
}

const contractDetailRef = ref()
const handleViewContract = () => {
  contractDetailRef.value.open(detail.contract)
}

const handleWithdraw = () => {}

const handleAudit = async (data, result) => {
  result ? await audit({ id: data.id }) : await unAudit({ id: data.id })
  message.success('保存成功')
  loadDetail(detail.id)
}
const handleRemove = () => {
  Modal.confirm({
    title: '确认删除该账单调整？',
    content: '',
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: detail.id })
      message.success('删除成功')
      visible.value = false
      emit('refresh', true)
    }
  })
}

const handleClose = () => {
  visible.value = false
  detail.billList = [] // 账单明细
}

defineExpose({ open })
</script>

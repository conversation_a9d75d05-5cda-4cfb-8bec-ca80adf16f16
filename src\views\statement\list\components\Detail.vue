<template>
  <a-drawer v-model:open="visible" :mask-closable="false" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === 0 }"
            @click="handleSwitch(1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === ids.length - 1 }"
            @click="handleSwitch()"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="primary-btn">反审批</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn">下载退款申请单</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">账单详情</h2>
        <a-tag :type="getStatusColor(detailData.status)">
          {{ detailData.status_dictText }}
        </a-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>

      <h2 class="text-[16px] font-bold mb-[12px] text-secondary">基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">客户：{{ detailData.customer || '-' }}</span>
        <span class="w-[50%]">合同：{{ detailData.contractNumber || '-' }}</span>
        <span class="w-[50%]">物业管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
        <span class="w-[50%]">业务日期：{{ '-' }}</span>
        <span class="w-[50%]">账单类型：{{ detailData.operatorDepart || '-' }}</span>
        <span class="w-[50%]">业务员：{{ detailData.operator || '-' }}</span>
        <span class="w-[50%]">业务部门：{{ detailData.status_dictText || '-' }}</span>
        <span class="w-[50%]">业务状态：{{ detailData.auditTime || '-' }}</span>
        <span class="w-[50%]">单据状态：{{ detailData.auditTime || '-' }}</span>
        <span class="w-[50%]">关闭原因：{{ detailData.auditTime || '-' }}</span>
        <span class="w-[50%]">说明：{{ detailData.auditTime || '-' }}</span>
      </div>

      <h2 class="text-[16px] font-bold mb-[12px] mt-[40px] text-secondary">账单明细</h2>
      <a-table
        :data-source="detailData.list"
        :columns="columns"
        :scroll="{ y: 300, x: 1500 }"
        :pagination="false"
      ></a-table>
    </a-spin>
  </a-drawer>
</template>
<script setup>
// import { detailById, queryDetailBillEntryByMainId } from '../apis'
defineEmits(['loadData'])
const { ids } = defineProps({
  ids: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    const activeIndex = ids.indexOf(data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    getDetailById(data.id)
  }
}
defineExpose({ open })
// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    AUDITOK: 'success', // 审核通过
    AUDITING: 'processing', // 审核中
    TEMP: 'default', // 暂存
    AUDITNO: 'error', // 审核不通过
    BACK: 'warning', // 已撤回
    ENABLE: 'success', // 启用
    DISABLE: 'error', // 禁用
    CLOSED: 'default', // 关闭
    INTEND: 'blue' // 意向
  }
  return statusMap[status] || 'default'
}
const loading = ref(false)
const detailData = ref({})
// 通过id获取详情
const getDetailById = (id) => {
  loading.value = true
  try {
    // const { result } = await detailById(id)
    // detailData.value = result
    getQueryDetailBillEntryByMainId(id)
  } finally {
    loading.value = false
  }
}
//
const getQueryDetailBillEntryByMainId = () => {
  // const { result } = await queryDetailBillEntryByMainId(id)
  // detailData.value.list = result
}

const handleSwitch = (type) => {
  // 上一条
  if (type) {
    if (curIndex.value > 0) {
      curIndex.value--
      getDetailById(ids[curIndex.value])
    }
    return
  }
  if (curIndex.value < ids.length - 1) {
    curIndex.value++
    getDetailById(ids[curIndex.value])
  }
}

const columns = [
  { title: '租赁单元', dataIndex: '', width: 150, fixed: true },
  { title: '片区管理员', dataIndex: '' },
  { title: '款项类型', dataIndex: '' },
  { title: '缴交周期', dataIndex: '' },
  { title: '是否押金', dataIndex: '' },
  { title: '产权归集公司', dataIndex: '' },
  { title: '应收日期', dataIndex: '' },
  { title: '开始日期', dataIndex: '' },
  { title: '到期日期', dataIndex: '' },

  { title: '归属年月', dataIndex: '' },
  { title: '款项金额', dataIndex: '' },
  { title: '减免金额', dataIndex: '' },
  { title: '实际应收', dataIndex: '' },

  { title: '已收金额', dataIndex: '' },
  { title: '未收金额', dataIndex: '' },
  { title: '已转款抵扣', dataIndex: '' },

  { title: '已退金额', dataIndex: '' },
  { title: '已处理尾差', dataIndex: '' },
  { title: '可转款抵扣金额', dataIndex: '' },

  { title: '已提房产税', dataIndex: '' }
]
</script>

<template>
  <a-drawer
    v-model:open="visible"
    :title="`${formData.id ? '编辑' : '新建'}退款申请`"
    class="edit-refund-drawer common-drawer"
    placement="right"
    width="1072px"
    :confirm-loading="confirmLoading"
    @close="handleCancel"
  >
    <a-spin :spinning="confirmLoading">
      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">基础信息</h4>
      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ style: { width: '140px' } }">
        <a-form-item label="业务时间" name="bizDate">
          <a-date-picker v-model:value="formData.bizDate" value-format="YYYY-MM-DD" style="width: 100%" />
        </a-form-item>

        <a-form-item label="退款客户" name="customer">
          <a-form-item-rest>
            <customer-select v-model="formData.customer" placeholder="请选择退款客户" />
          </a-form-item-rest>
        </a-form-item>

        <a-form-item label="经办人" name="operator">
          <a-form-item-rest><user-select v-model="formData.operator" placeholder="请选择经办人" /></a-form-item-rest>
        </a-form-item>

        <a-form-item label="业务部门" name="operatorDepart">
          <dept-tree-select v-model:value="formData.operatorDepart" placeholder="请选择业务部门" style="width: 100%" />
        </a-form-item>

        <a-form-item label="备注" name="remark" class="form-item-full">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注信息" :rows="4" />
        </a-form-item>
      </a-form>

      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c] flex justify-between">
        退款明细
        <a-button type="primary" @click="handleAddRefundItem">添加明细</a-button>
      </h4>

      <a-table
        v-if="formData.refundReqBillEntryList.length"
        :columns="refundColumns"
        :data-source="formData.refundReqBillEntryList"
        :pagination="false"
        size="middle"
        bordered
        :scroll="{ x: 2000 }"
        :custom-row="customRow"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'detailBill'">
            <i
              v-show="hoveredRowId === record.id || hoveredRowId === index"
              class="a-icon-remove cursor-pointer mr-2 text-red-500"
              @click="handleRemoveRefundItem(index)"
            ></i>
            {{ record.detailBill }}
          </template>
          <template v-else-if="column.dataIndex === 'thisRefundAmt'">
            <a-input-number
              v-model:value="record.thisRefundAmt"
              :min="0"
              :precision="2"
              placeholder="请输入退款金额"
              style="width: 100%"
            />
          </template>
          <template v-else-if="column.dataIndex === 'remark'">
            <a-input v-model:value="record.remark" placeholder="请输入备注" style="width: 100%" />
          </template>
        </template>
      </a-table>
      <div v-else class="flex flex-col items-center">
        <img src="@/assets/imgs/no-data.png" />
        <span class="text-tertiary">暂无数据</span>
      </div>

      <div class="p-[16px] bg-[#f7f8fa] rounded-[8px] mt-[40px] border border-[#e6e9f0]">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-[24px] text-[#1d335c]">
              <span v-if="formData.refundReqAmount < 0">应退：</span>
              <span v-else>调整后待核销合计：</span>
              <span class="text-[#f03a1d] font-bold">{{ formData.refundReqAmount.toFixed(2) }}</span>
            </p>
          </div>
        </div>
      </div>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit">提交</a-button>
      <a-button @click="handleTemporaryStorage">暂存</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>

  <!-- 退款明细选择器 -->
  <refund-detail-selector ref="refundDetailSelectorRef" @confirm="handleRefundDetailConfirm" />
</template>

<script setup>
import { message } from 'ant-design-vue'
import { addRefundReqBill, editRefundReqBill, submitRefundReqBill, queryRefundReqBillEntries } from '../apis'
import RefundDetailSelector from './RefundDetailSelector.vue'

const emits = defineEmits(['refresh'])

const visible = ref(false)
const confirmLoading = ref(false)
const formRef = ref()
const refundDetailSelectorRef = ref()
const hoveredRowId = ref(null)

const formDataDefault = {
  bizDate: undefined,
  customer: undefined,
  operator: undefined,
  operatorDepart: undefined,
  remark: undefined,
  refundReqAmount: 0,
  refundReqBillEntryList: []
}

const formData = reactive({ ...formDataDefault })

const rules = {
  bizDate: [{ required: true, message: '请选择业务日期' }],
  customer: [{ required: true, message: '请选择退款客户' }],
  operator: [{ required: true, message: '请选择经办人' }],
  operatorDepart: [{ required: true, message: '请选择业务部门' }]
}

/**
 * 退款明细表格列配置
 */
const refundColumns = [
  { title: '账单编号', dataIndex: 'detailBill', width: 200, fixed: 'left' },
  { title: '客户', dataIndex: 'customer_dictText', width: 150 },
  { title: '合同', dataIndex: 'contract_dictText', width: 150 },
  { title: '款项类型', dataIndex: 'paymentType_dictText', width: 120 },
  { title: '归属年月', dataIndex: 'incomeBelongYm', width: 100 },
  { title: '期数/总期数', dataIndex: 'periodTotalPeriod', width: 120 },
  { title: '应收日期', dataIndex: 'receiveDate', width: 120 },
  { title: '开始日期', dataIndex: 'receiveBeginDate', width: 120 },
  { title: '到期日期', dataIndex: 'receiveEndDate', width: 120 },
  { title: '款项金额', dataIndex: 'paymentAmount', width: 120 },
  { title: '减免金额', dataIndex: 'remission', width: 120 },
  { title: '实际应收', dataIndex: 'actualReceiveAmount', width: 120 },
  { title: '已收金额', dataIndex: 'paid', width: 120 },
  { title: '未收金额', dataIndex: 'residual', width: 120 },
  { title: '已转款抵扣', dataIndex: 'transferDeduction', width: 120 },
  { title: '已退金额', dataIndex: 'refunded', width: 120 },
  { title: '已处理尾差', dataIndex: 'offDifference', width: 120 },
  { title: '剩余可转', dataIndex: 'residueRefundAmount', width: 120, fixed: 'right' },
  {
    title: '本次转款金额',
    dataIndex: 'thisRefundAmt',
    width: 150,
    fixed: 'right',
    customRender: ({ record }) => {
      return record.thisRefundAmt || 0
    }
  },
  { title: '备注', dataIndex: 'remark', width: 160, fixed: 'right' }
]

/**
 * 打开抽屉并初始化表单数据
 * @param {Object} record - 编辑时的记录数据，新建时为空
 */
const open = async (record) => {
  visible.value = true
  if (record?.id) {
    Object.assign(formData, record)
    // 加载退款申请单分录列表
    await loadRefundEntries(record.id)
  }
}

/**
 * 加载退款申请单分录列表
 * @param {string} mainId - 主表ID
 */
const loadRefundEntries = async (mainId) => {
  try {
    confirmLoading.value = true
    const response = await queryRefundReqBillEntries({ id: mainId })
    if (response.success && response.result) {
      formData.refundReqBillEntryList = response.result
    }
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 取消操作，关闭抽屉并重置表单
 */
const handleCancel = () => {
  Object.assign(formData, formDataDefault)
  formData.refundReqBillEntryList = []
  emits('refresh')
  visible.value = false
}

/**
 * 打开退款明细选择器
 */
const handleAddRefundItem = () => {
  // 获取已选择的明细ID数组
  const selectedDetailIds = formData.refundReqBillEntryList.map((item) => item.detailBillEntry || item.id)
  refundDetailSelectorRef.value?.open(selectedDetailIds)
}

/**
 * 退款明细选择确认回调
 * @param {Array} selectedData - 选中的明细数据
 */
const handleRefundDetailConfirm = (selectedData) => {
  // 获取当前已存在的明细ID集合
  const existingDetailIds = new Set(formData.refundReqBillEntryList.map((item) => item.detailBillEntry || item.id))

  // 过滤出新的明细（不重复的）
  const newItems = selectedData
    .filter((item) => !existingDetailIds.has(item.id))
    .map((item) => ({
      ...item,
      detailBill: item.number,
      detailBillEntry: item.id,
      thisRefundAmt: 0,
      residueRefundAmount: item.balance || 0,
      remark: ''
    }))

  // 只添加新的明细
  if (newItems.length > 0) {
    formData.refundReqBillEntryList.push(...newItems)
    message.success(`已添加 ${newItems.length} 条新的退款明细`)
  } else {
    message.info('所选明细均已存在，未添加新的明细')
  }
}

/**
 * 删除退款明细项
 * @param {number} index - 要删除的行索引
 */
const handleRemoveRefundItem = (index) => {
  formData.refundReqBillEntryList.splice(index, 1)
}

/**
 * 自定义行属性，处理鼠标悬停事件
 * @param {Object} record - 行数据
 * @param {number} index - 行索引
 */
const customRow = (record, index) => {
  return {
    onMouseenter: () => {
      hoveredRowId.value = record.id || index
    },
    onMouseleave: () => {
      hoveredRowId.value = null
    }
  }
}

/**
 * 验证明细数据
 */
const validateDetails = () => {
  // 验证退款明细
  for (let i = 0; i < formData.refundReqBillEntryList.length; i++) {
    const item = formData.refundReqBillEntryList[i]
    if (!item.thisRefundAmt || item.thisRefundAmt < 0) {
      message.error(`退款明细第 ${i + 1} 行的本次退款金额必须大于0`)
      return false
    }

    // 校验本次转款金额不能大于剩余可转
    if (item.thisRefundAmt > item.residueRefundAmount) {
      message.error(`退款明细第 ${i + 1} 行的本次转款金额不能大于剩余可转金额`)
      return false
    }
  }

  return true
}

/**
 * 格式化退款明细数据，确保符合后端API数据结构
 * @param {Array} entryList - 原始明细数据
 * @returns {Array} 格式化后的明细数据
 */
const formatRefundEntryList = (entryList) => {
  return entryList.map((item) => ({
    id: item.id || undefined,
    parent: formData.id || undefined,
    detailBillEntry: item.number || '',
    thisRefundAmt: item.thisRefundAmt || 0,
    remark: item.remark || ''
  }))
}

/**
 * 通用保存逻辑
 * @param {boolean} isTemporary - 是否为暂存（true: 暂存, false: 正式保存）
 */
const saveData = async (isTemporary = false) => {
  if (confirmLoading.value) return

  confirmLoading.value = true

  try {
    await formRef.value?.validate()

    // 验证明细数据
    if (!validateDetails()) {
      return
    }

    // 格式化退款明细数据，确保符合后端API数据结构
    const formattedData = {
      ...formData,
      refundReqBillEntryList: formatRefundEntryList(formData.refundReqBillEntryList)
    }

    // 根据操作类型选择对应的API
    const api = formData.id ? editRefundReqBill : isTemporary ? addRefundReqBill : submitRefundReqBill

    await api(formattedData)

    const action = isTemporary ? '暂存' : formData.id ? '编辑' : '添加'
    message.success(`退款申请${action}成功`)

    handleCancel()
  } finally {
    confirmLoading.value = false
  }
}

/**
 * 提交退款申请
 */
const handleSubmit = () => saveData(false)

/**
 * 暂存退款申请
 */
const handleTemporaryStorage = () => saveData(true)

/**
 * 监听退款明细列表变化，自动更新退款总金额
 */
watch(
  () => formData.refundReqBillEntryList,
  (newList) => {
    formData.refundReqAmount = newList.reduce((total, item) => {
      return total + (item.thisRefundAmt || 0)
    }, 0)
  },
  { deep: true }
)

defineExpose({
  open
})
</script>

<style lang="less" scoped>
.edit-refund-drawer {
  .ant-form {
    display: flex;
    flex-wrap: wrap;
    gap: 0 20px;
  }

  .ant-form-item {
    width: calc(50% - 10px);
  }

  .form-item-full {
    width: 100%;
  }

  .ant-picker {
    width: 100%;
  }

  .ant-form-item-control {
    display: flex;
  }
}
</style>

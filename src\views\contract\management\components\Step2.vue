<template>
  <div class="edit-contract-step2">
    <div class="flex items-center justify-between">
      <strong class="text-[16px]">合同款项</strong>
      <a-dropdown>
        <a-button type="primary" size="medium">
          添加款项
          <i class="a-icon-arrow-down ml-[8px]"></i>
        </a-button>
        <template #overlay>
          <a-menu>
            <a-menu-item v-for="item in paymentTypeList" :key="item.id" @click="handleSelectPaymentType(item)">
              {{ item.name }}
            </a-menu-item>
          </a-menu>
        </template>
      </a-dropdown>
    </div>
    <div class="h-[36px] flex items-center bg-[#f5f7fa] my-[20px] text-secondary px-[16px] rounded-[8px]">
      <i class="a-icon-tips mr-[8px]"></i>
      说明: 水电费将根据租赁单元对应的水电均摊方式自动出账，无需在此定义。
    </div>
    <div>
      <a-form
        v-for="(item, index) in form.contractLeaseFundsList"
        :key="item.id"
        :model="item"
        ref="formRefs"
        label-align="left"
        :label-col="{ style: { width: '94px' } }"
        :rules="rules"
        autocomplete="off"
      >
        <div class="flex items-center justify-between mb-[16px]">
          <strong>{{ item.paymentTypeName }}</strong>
          <a-popconfirm title="是否确认移除？" @confirm="handleRemove(index)">
            <span>
              <i class="a-icon-remove text-[18px] cursor-pointer text-error"></i>
            </span>
          </a-popconfirm>
        </div>
        <div class="grid-box">
          <a-form-item label="缴交周期" name="period">
            <a-input v-model:value="item.period" :maxlength="5">
              <template #suffix>月</template>
            </a-input>
          </a-form-item>
          <a-form-item label="金额" name="amountPerMonth">
            <a-input v-model:value="item.amountPerMonth" :maxlength="8">
              <template #suffix>元/月</template>
            </a-input>
          </a-form-item>
          <a-form-item label="起止日期" name="dateRange">
            <a-range-picker v-model:value="item.dateRange" value-format="YYYY-MM-DD" />
          </a-form-item>
        </div>
        <div class="grid-box">
          <a-form-item label="账单生成规则" name="createDetailBill">
            <dict-select v-model="item.createDetailBill" code="CT_BASE_ENUM_Contract_CreateDetailBill"></dict-select>
          </a-form-item>
          <a-form-item label="应收日" name="advanceDays">
            <a-input v-model:value="item.advanceDays">
              <template #prefix>比开始日期提前</template>
              <template #suffix>天</template>
            </a-input>
          </a-form-item>
          <a-form-item>
            <template #label>
              <span class="mr-[4px]">递增规则</span>
              <a-tooltip>
                <template #title>前往管理递增规则</template>
                <span>
                  <i class="a-icon-setting cursor-pointer text-primary" @click="toPriceIncreasePage"></i>
                </span>
              </a-tooltip>
            </template>
            <a-select
              v-model:value="item.priceIncreaseWay"
              :options="ruleList"
              allow-clear
              show-search
              :filter-option="filterOption"
              :field-names="{ label: 'name', value: 'id' }"
            ></a-select>
          </a-form-item>
        </div>
        <a-form-item label="备注" name="remark">
          <a-input v-model:value="item.remark" :maxlength="200"></a-input>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup>
import { getContractFundTypeList } from '../apis'
import { message } from 'ant-design-vue'
import { validatePositionInt, validateMoney, validateInteger } from '@/utils/validate'
import { page } from '@/views/contract/priceIncrease/apis'

const { form } = defineProps({
  form: { required: true, type: Object }
})

const emit = defineEmits(['toPriceIncreasePage'])

const paymentTypeList = ref([])
const loadPaymentTypeList = async () => {
  const { result } = await getContractFundTypeList()
  paymentTypeList.value = result.records
}

const handleSelectPaymentType = (item) => {
  if (form.contractLeaseFundsList.some((i) => i.paymentType === item.id)) {
    message.warning(`您已添加了“${item.name}”，请勿重复添加`)
    return
  }
  form.contractLeaseFundsList.push({
    id: Math.random().toString(),
    paymentType: item.id,
    paymentTypeName: item.name,
    period: '', // 缴交周期
    amountPerMonth: '', // 金额
    amountPerPeriod: '', // 每期金额，自动生成（缴费期数*月租金）
    startDate: '',
    expireDate: '',
    dateRange: [], // startDate && expireDate
    createDetailBill: '', // 账单生成规则
    advanceDays: '', // 应收提前天数
    remark: '',
    openRule: false, // 是否开启递增规则
    priceIncreaseWay: '' // 价格递增方式
  })
}

const handleRemove = (index) => {
  form.contractLeaseFundsList.splice(index, 1)
}

const ruleList = ref([]) // 递增规则列表
const loadRuleList = async () => {
  const { result } = await page()
  ruleList.value = result.records
}

const filterOption = (input, option) => {
  return option.name.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const rules = {
  period: [{ required: true, validator: validatePositionInt(true, '缴交周期'), trigger: 'blur' }],
  amountPerMonth: [{ required: true, validator: validateMoney(true, '金额'), trigger: 'blur' }],
  dateRange: [{ required: true, type: 'array', message: '请选择起止日期', trigger: 'change' }],
  createDetailBill: [{ required: true, message: '请选择账单生成规则', trigger: 'change' }],
  advanceDays: [{ required: true, validator: validateInteger(true, '应收日'), trigger: 'blur' }]
}

const formRefs = ref([])

/**
 * 校验字段是否都填写正确
 * @param opeType isSave=是否暂存 如果是暂存的话，则合同款项可以不添加，但是如果添加了，就要校验字段是否正确
 */
const validate = async (isSave = false) => {
  if (!isSave && !form.contractLeaseFundsList) {
    message.warning('请添加合同款项')
    return false
  }
  const result = await Promise.allSettled(formRefs.value.map((formRef) => formRef.validate()))
  const index = result.findIndex((i) => i.status === 'rejected')
  if (index !== -1) {
    const container = document.querySelector('.edit-contract-step2')
    container.querySelectorAll('.ant-form')[index].scrollIntoView({ behavior: 'smooth' })
  }
  return index === -1
}

// 前往价格递增方式管理页面
const toPriceIncreasePage = () => {
  emit('toPriceIncreasePage')
}

onMounted(() => {
  loadPaymentTypeList()
  loadRuleList()
})

defineExpose({ validate })
</script>

<style lang="less" scoped>
:deep(.ant-form) {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0;
  }
  .ant-input-prefix {
    color: var(--color-main) !important;
  }
}
.grid-box {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0 16px;
}
</style>

<template>
  <a-drawer v-model:open="visible" :mask-closable="false" class="common-detail-drawer" placement="right" width="1072px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === 0 }"
            @click="handleSwitch(1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': curIndex === ids.length - 1 }"
            @click="handleSwitch()"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <!-- 暂存、撤回、不通过的才能编辑提交？ -->
          <span v-if="['TEMP', 'BACK', 'AUDITNO'].includes(detailData.status)" class="primary-btn">调整</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down text-[12px]"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item>
                  <span class="primary-btn">查看合同</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn">新增核销</span>
                </a-menu-item>
                <a-menu-item>
                  <span class="primary-btn">查看核销记录</span>
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px]">应收明细</h2>
        <a-tag :type="getStatusColor(detailData.status)">
          {{ detailData.status_dictText }}
        </a-tag>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>编号: {{ detailData.number || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span>{{ detailData.createBy_dictText }} 创建于{{ detailData.createTime }}</span>
      </div>

      <h2 class="text-[16px] font-bold mb-[12px] text-secondary">基础信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">应收客户：{{ detailData.customer || '-' }}</span>
        <span class="w-[50%]">合同：{{ detailData.contractNumber || '-' }}</span>
        <span class="w-[50%]">物业管理公司：{{ detailData.manageCompany_dictText || '-' }}</span>
        <span class="w-[50%]">产权归集公司：{{ '-' }}</span>
        <span class="w-[50%]">经办部门：{{ detailData.operatorDepart_dictText || '-' }}</span>
        <span class="w-[50%]">经办人：{{ detailData.operator_dictText || '-' }}</span>
        <span class="w-[50%]">业务状态：{{ detailData.status_dictText || '-' }}</span>
        <span class="w-[50%]">完成核销日期：{{ detailData.auditTime || '-' }}</span>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px] mt-[40px] text-secondary">款项信息</h2>
      <div class="flex flex-wrap gap-y-[12px] text-secondary">
        <span class="w-[50%]">款项类型：{{ '-' }}</span>
        <span class="w-[50%]">应收日期：{{ detailData.receiveDate || '-' }}</span>
        <span class="w-[50%]">起止时间：{{ '-' }}</span>
        <span class="w-[50%]">期数/总期数：{{ '-' }}</span>
        <span class="w-[50%]">归属月份：{{ '-' }}</span>
        <span class="w-[50%]">租赁单元：{{ '-' }}</span>
        <span class="w-[50%]">税额：{{ detailData.taxAmount || '-' }}</span>
        <span class="w-[50%]">应收金额：{{ '-' }}</span>
        <span class="w-[50%]">待核销金额：{{ '-' }}</span>
        <span class="w-[50%]">已核销金额：{{ '-' }}</span>
      </div>
      <div class="flex items-center justify-between mb-[12px] mt-[40px] text-secondary">
        <h2 class="text-[16px] font-bold">对应核销明细</h2>
        <div class="flex items-center justify-between">
          <span class="mr-[16px]">剩余待核销：-</span>
          <a-button type="primary" ghost>
            <span class="a-icon-plus mr-[8px]"></span>
            继续核销
          </a-button>
        </div>
      </div>
      <a-table :data-source="detailData.list" :columns="columns" :scroll="{ y: 300, x: 1000 }" :pagination="false">
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <span class="primary-btn" @click="rowView(record)">收付款详情</span>
          </template>
        </template>
      </a-table>
    </a-spin>
  </a-drawer>
</template>
<script setup>
import { detailById, queryReceiveBillEntryByMainId } from '../apis'
defineEmits(['loadData'])
const { ids } = defineProps({
  ids: {
    type: Array,
    default: () => {
      return []
    }
  }
})
const curIndex = ref(0)
const visible = ref(false)
/**
 * 打开弹窗
 * @param {Object} data 编辑菜单时，才有的数据，从列表带过来
 */
const open = (data = {}) => {
  visible.value = true
  if (data.id) {
    const activeIndex = ids.indexOf(data.id)
    curIndex.value = activeIndex === -1 ? 0 : activeIndex
    getDetailById(data.id)
  }
}
defineExpose({ open })
// 获取状态颜色
const getStatusColor = (status) => {
  const statusMap = {
    AUDITOK: 'success', // 审核通过
    AUDITING: 'processing', // 审核中
    TEMP: 'default', // 暂存
    AUDITNO: 'error', // 审核不通过
    BACK: 'warning', // 已撤回
    ENABLE: 'success', // 启用
    DISABLE: 'error', // 禁用
    CLOSED: 'default', // 关闭
    INTEND: 'blue' // 意向
  }
  return statusMap[status] || 'default'
}
const loading = ref(false)
const detailData = ref({})
// 通过id获取详情
const getDetailById = async (id) => {
  loading.value = true
  try {
    const { result } = await detailById(id)
    detailData.value = result
    getQueryDetailBillEntryByMainId(id)
  } finally {
    loading.value = false
  }
}
//
const getQueryDetailBillEntryByMainId = async (id) => {
  const { result } = await queryReceiveBillEntryByMainId(id)
  detailData.value.list = result
}

const handleSwitch = (type) => {
  // 上一条
  if (type) {
    if (curIndex.value > 0) {
      curIndex.value--
      getDetailById(ids[curIndex.value])
    }
    return
  }
  if (curIndex.value < ids.length - 1) {
    curIndex.value++
    getDetailById(ids[curIndex.value])
  }
}

const columns = [
  { title: '单据编号', dataIndex: 'number', width: 150, fixed: true },
  { title: '核销金额', dataIndex: '' },
  { title: '核销时间', dataIndex: '' },
  { title: '核销人', dataIndex: 'operator_dictText' },
  { title: '收付款记录', dataIndex: '' },
  { title: '应收开始时间', dataIndex: 'receiveBeginDate' },
  { title: '应收结束时间', dataIndex: 'receiveEndDate' },
  { title: '操作', dataIndex: 'action', width: 150, fixed: 'right' }
]

const rowView = () => {}
</script>

<!-- 资产选择弹窗 -->
<template>
  <a-modal
    class="common-modal"
    v-model:open="visible"
    width="70%"
    title="选择资产"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="mb-[12px]">
      <a-form autocomplete="off" layout="inline">
        <a-form-item label="类别">
          <api-tree-select
            class="!w-[280px]"
            v-model="search.treeId"
            :async-fn="getQueryTreeList"
            :field-names="{ label: 'name', value: 'id' }"
            placeholder="请选择类别"
            @change="handleInput"
          ></api-tree-select>
        </a-form-item>
        <a-form-item label="搜索">
          <s-input class="!w-[280px]" v-model:value="search.name" placeholder="请输入" @input="handleInput"></s-input>
        </a-form-item>
        <search-more
          v-model="searchFilter"
          :search-list="searchList"
          @filterItemChange="filterItemChange"
          @searchChange="onTableChange"
        ></search-more>
      </a-form>
    </div>
    <a-table
      ref="tableRef"
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :scroll="{ y: 600, x: 2000 }"
      :pagination="pagination"
      row-key="id"
      :row-selection="{
        selectedRowKeys,
        selectedRows,
        type: multiple ? 'checkbox' : 'radio',
        onChange: onSelectChange
      }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'status'">
          <a-switch v-model:checked="record.checked" @change="handleStatusChange(record, $event)" />
        </template>
        <template v-if="column.dataIndex === 'detailAddress'">
          <div>{{ renderAddressName(record.pcaCode) }}{{ record.detailAddress || '-' }}</div>
        </template>
        <template v-if="column.dataIndex === 'layerNum'">
          <div>
            <span>{{ record.layerNum }}</span>
            <span v-if="record.layerNum && record.totalLayerNum">/</span>
            <span>{{ record.totalLayerNum }}</span>
          </div>
        </template>
      </template>
    </a-table>
  </a-modal>
</template>
<script setup>
import { renderDict, renderDictTag, renderAddressName } from '@/utils/render'
import areaList from '@/json/region.json'
import {
  getPage,
  getQueryWyBuildingByMainId,
  getQueryWyFloorByMainId,
  getQueryTreeList,
  updateEnableDisableStatus
} from '@/views/assets/manage/apis.js'
import { projectPage } from '@/views/projects/apis.js'
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { isOrNotDic } from '@/store/modules/dict.js'
import { message } from 'ant-design-vue'
// import { getCustomerList } from '@/views/customer/manage/apis'
import { getUserList } from '@/views/system/user/apis'
const { multiple, asyncFunc } = defineProps({
  multiple: { default: false, type: Boolean },
  asyncFunc: { default: getPage, type: Function }
})
const visible = ref(false)
const search = ref({
  treeId: '',
  name: ''
})

const searchFilter = ref({
  number: undefined,
  wyProject: '',
  wyBuilding: '',
  wyFloor: '',
  pcaCode: [],
  collectionCompany: '',
  ownerCompany: '',
  manageCompany: '',
  assetsType: '',
  bizStatus: '',
  status: '',
  acquisitionMethod: '',
  propertyRightStatus: '',
  propertyUse: '',
  landNature: '',
  landConstructionSituation: '',
  houseType: '',
  buildStructrue: '',
  houseModel: undefined,
  firefightingRate: '',
  houseSafeRate: '',
  createTime: undefined,
  createBy: '',
  updateTime: undefined,
  updateBy: '',
  auditTime: undefined,
  auditBy: '',
  detailAddress: undefined,
  warrantsDate: undefined,
  isUnionCertificate: undefined,
  buildYear: undefined
})
const wyBuildingPar = reactive({ id: '' })
const getQueryWyBuildingByMainIdFunc = computed(() => {
  wyBuildingPar.id
  return () => getQueryWyBuildingByMainId(wyBuildingPar)
})
const wyFloorPar = reactive({ id: '' })
const getQueryWyFloorByMainIdFunc = computed(() => {
  wyFloorPar.id
  return () => getQueryWyFloorByMainId(wyFloorPar)
})
const searchList = reactive([
  { label: '资产编号', name: 'number', type: 'input', placeholder: '请输入资产编号' },
  { label: '权证号', name: 'ownerNumber', type: 'input', placeholder: '请输入权证号' },
  {
    label: '关联项目',
    name: 'wyProject',
    type: 'api',
    placeholder: '请选择关联项目',
    listFunc: projectPage
  },
  {
    label: '楼栋',
    name: 'wyBuilding',
    type: 'api',
    placeholder: '请选择楼栋',
    listFunc: getQueryWyBuildingByMainIdFunc
  },
  {
    label: '楼层',
    name: 'wyFloor',
    type: 'api',
    placeholder: '请选择楼层',
    listFunc: getQueryWyFloorByMainIdFunc
  },
  { label: '区域', name: 'pcaCode', type: 'cascader', placeholder: '请选择区域', list: areaList },
  { label: '租金归集公司', name: 'collectionCompany', type: 'deptTree', placeholder: '请选择租金归集公司' },
  { label: '资产权属公司', name: 'ownerCompany', type: 'deptTree', placeholder: '请选择资产权属公司' },
  { label: '物业管理公司', name: 'manageCompany', type: 'deptTree', placeholder: '请选择物业管理公司' },
  { label: '资产类型', name: 'assetsType', type: 'dic', placeholder: '请选择资产类型', code: 'CT_BAS_AssetsType' },
  {
    label: '业务状态',
    name: 'bizStatus',
    type: 'dic',
    placeholder: '请选择业务状态',
    code: 'CT_BASE_ENUM_HouseOwner_BizStatus'
  },
  { label: '启用状态', name: 'status', type: 'dic', placeholder: '请选择启用状态', code: 'CT_BASE_ENUM_AuditStatus' },
  {
    label: '取得来源',
    name: 'acquisitionMethod',
    type: 'dic',
    placeholder: '请选择取得来源',
    code: 'CT_BAS_AcquisitionMethod'
  },
  {
    label: '产权情况',
    name: 'propertyRightStatus',
    type: 'dic',
    placeholder: '请选择产权情况',
    code: 'CT_BASE_ENUM_HouseOwner_PropertyRightStatus'
  },
  { label: '产权用途', name: 'propertyUse', type: 'dic', placeholder: '请选择产权用途', code: 'CT_BAS_PropertyUse' },
  { label: '使用权类型', name: 'landNature', type: 'dic', placeholder: '请选择使用权类型', code: 'CT_BAS_LandNature' },
  {
    label: '土地建设情况',
    name: 'landConstructionSituation',
    type: 'dic',
    placeholder: '请选择土地建设情况',
    code: 'CT_BAS_LandCS'
  },
  {
    label: '房产类型',
    name: 'houseType',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BASE_ENUM_HouseOwner_HouseType'
  },
  {
    label: '建筑结构',
    name: 'buildStructrue',
    type: 'dic',
    placeholder: '请选择房产类型',
    code: 'CT_BAS_BuildStructrue'
  },
  { label: '户型', name: 'houseModel', type: 'input', placeholder: '请输入户型' },
  {
    label: '消防等级',
    name: 'firefightingRate',
    type: 'dic',
    placeholder: '请选择消防等级',
    code: 'CT_BAS_FirefightingRate'
  },
  {
    label: '房屋安全等级',
    name: 'houseSafeRate',
    type: 'dic',
    placeholder: '请选择房屋安全等级',
    code: 'CT_BAS_HouseSafeRate'
  },
  { label: '创建时间', name: 'createTime', type: 'date', placeholder: '请选择创建时间' },
  {
    label: '创建人',
    name: 'createBy',
    type: 'api',
    placeholder: '请选择创建人',
    listFunc: () => getUserList({ pageNo: 1, pageSize: 10000 }),
    fieldNames: { label: 'realname', value: 'id' }
  },
  { label: '最近修改时间', name: 'updateTime', type: 'date', placeholder: '请选择最近修改时间' },
  {
    label: '最近修改人',
    name: 'updateBy',
    type: 'api',
    placeholder: '请选择最近修改人',
    listFunc: () => getUserList({ pageNo: 1, pageSize: 10000 }),
    fieldNames: { label: 'realname', value: 'id' }
  },
  { label: '审核时间', name: 'auditTime', type: 'date', placeholder: '请选择审核时间' },
  {
    label: '审核人',
    name: 'auditBy',
    type: 'api',
    placeholder: '请选择审核人',
    listFunc: () => getUserList({ pageNo: 1, pageSize: 10000 }),
    fieldNames: { label: 'realname', value: 'id' }
  },
  { label: '详细地址', name: 'detailAddress', type: 'input', placeholder: '请输入详细地址' },
  { label: '权证获得日期', name: 'warrantsDate', type: 'date', placeholder: '请选择权证获得日期' },
  {
    label: '房地权证合一',
    name: 'isUnionCertificate',
    type: 'select',
    placeholder: '请选择房地权证合一',
    list: isOrNotDic
  },
  { label: '建筑年份', name: 'buildYear', type: 'year', format: 'YYYY', placeholder: '请选择建筑年份' }
])
const columns = [
  { title: '资产名称', dataIndex: 'name', width: 150, fixed: true },
  { title: '权证号', dataIndex: 'ownerNumber' },
  { title: '产权用途', dataIndex: 'propertyUse' },
  {
    title: '使用权类型',
    dataIndex: 'landNature',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_LandNature')
  },
  { title: '地址', dataIndex: 'detailAddress' },
  {
    title: '启用状态',
    dataIndex: 'status',
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_BizStatus')
  },
  { title: '建筑面积', dataIndex: 'structureArea' },
  { title: '宗地面积', dataIndex: 'floorArea' },
  {
    title: '房产类型',
    dataIndex: 'houseType',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_HouseOwner_HouseType')
  },
  {
    title: '资产类型',
    dataIndex: 'assetsType',
    customRender: ({ text }) => renderDict(text, 'CT_BAS_AssetsType')
  },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText' },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '管理公司', dataIndex: 'manageCompany_dictText' },
  { title: '层数/总层数', dataIndex: 'layerNum', width: 120 },
  { title: '备注', dataIndex: 'remark' },
  { title: '资产编号', dataIndex: 'number' }
]
const open = (selectedList = []) => {
  onTableChange()
  if (selectedList && selectedList.length > 0) {
    const selectedIds = selectedList.map((item) => item.id)
    selectedRowKeys.value = selectedIds
    // 根据 id 从 list 中获取完整的对象数据
    selectedRows.value = list.value.filter((item) => selectedIds.includes(item.id))
  }
  visible.value = true
}
defineExpose({ open })
const { list, pagination, tableLoading, onTableFetch } = usePageTable(asyncFunc, (list) => {
  list.forEach((item) => {
    item.loading = false
    item.checked = Boolean(item.status === 'ENABLE')
  })
  return list
})
const { selectedRowKeys, selectedRows, onSelectChange, clearSelection } = useTableSelection(list, 'id')
const onTableChange = ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({
    pageNo: current ?? pageNo,
    pageSize,
    ...search.value,
    ...searchFilter.value,
    pcaCode: searchFilter.value.pcaCode ? searchFilter.value.pcaCode.join(',') : ''
  })
}

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({
      pageNo: 1,
      pageSize: pagination.value.pageSize
    })
  }, 600)
}
// 筛选项字段和对应的值
const filterItemChange = (id, name) => {
  // 关联项目
  if (name === 'wyProject') {
    searchList.forEach((item) => {
      if (item.name === 'wyBuilding') {
        wyBuildingPar.id = id
      }
    })
  }
  // 楼栋
  if (name === 'wyBuilding') {
    searchList.forEach((item) => {
      if (item.name === 'wyFloor') {
        wyFloorPar.id = id
      }
    })
  }
}
const handleStatusChange = async (data, val) => {
  if (data.loading) return
  try {
    data.loading = true
    await updateEnableDisableStatus({ ids: data.id, status: val ? 'ENABLE' : 'DISABLE' })
    data.loading = false
    message.success('保存成功')
    data.status = val ? 'ENABLE' : 'DISABLE'
  } catch {
    data.loading = false
    data.checked = !val
  }
}

const emits = defineEmits(['selectChange'])
const handleOk = () => {
  if (selectedRows.value.length === 0) {
    message.warning('请至少选择一条数据')
    return
  }
  emits('selectChange', selectedRows.value)
  visible.value = false
}
/**
 * 取消选择
 */
const handleCancel = () => {
  clearSelection()
  visible.value = false
}
</script>

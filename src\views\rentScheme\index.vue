<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[28px] mb-[16px]">
      <div class="flex">
        <a-button type="primary" v-permission="'biz.leasemanage:ct_biz_rent_scheme:add'" @click="handleAdd">
          <i class="a-icon-plus"></i>
          新建招租方案
        </a-button>
        <a-button @click="handleImport">
          <i class="a-icon-import-right"></i>
          导入
        </a-button>
        <a-button :loading="exportLoading" @click="handleExport">
          <i class="a-icon-export-right"></i>
          导出
        </a-button>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="params.title"
          placeholder="搜索名称"
          class="ml-[40px] !w-[280px]"
          @input="handleInput"
        ></s-input>
        <filter-more
          :params="params"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :scroll="{ x: 1500, y: tableHeight }"
      v-model:expanded-row-keys="expandedRowKeys"
      @change="onTableChange"
      @expand="handleExpand"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn" @click="handleView(record)">查看</span>
          <a-dropdown>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
            <template #overlay>
              <a-menu>
                <a-menu-item @click="handleAudit(record, true)">审核(临时功能)</a-menu-item>
                <a-menu-item @click="handleEdit(record)" v-if="['TEMP', 'BACK', 'AUDITNO'].includes(record.status)">
                  编辑
                </a-menu-item>
                <a-menu-item @click="handleRemove(record)" v-if="['TEMP', 'BACK'].includes(record.status)">
                  删除
                </a-menu-item>
                <a-menu-item @click="handleWithdraw(record)" v-if="['AUDITING'].includes(record.status)">
                  撤回
                </a-menu-item>
                <a-menu-item @click="handleViewCustomer(record)" v-if="record.bidResult">查看中标客户</a-menu-item>
                <a-menu-item @click="handleViewContract(record)" v-if="record.contract">查看中标合同</a-menu-item>
                <a-menu-item
                  @click="handleSetResult(record)"
                  v-if="record.bizStatus === 'Listing' && record.status === 'AUDITOK'"
                >
                  招标结果
                </a-menu-item>
                <a-menu-item
                  @click="handleSetResult(record)"
                  v-if="record.bizStatus === 'FailedBidding' && record.status === 'AUDITOK'"
                >
                  再次招标
                </a-menu-item>
                <a-menu-item
                  @click="handleCreateContract(record)"
                  v-if="record.bizStatus === 'SuccessBidding' && !record.contract"
                >
                  创建合同
                </a-menu-item>
                <a-menu-item @click="handleAudit(record, false)" v-if="['AUDITOK'].includes(record.status)">
                  反审核
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </template>
      </template>
      <template #expandedRowRender="{ record }">
        <ul class="pl-[48px]">
          <li v-for="item in record.logList" :key="item.id" class="log-li">
            <p>{{ item.name }}</p>
            <div class="flex gap-x-[40px] mt-[12px]" v-if="item.name === '创建方案'">
              <span>创建人: {{ item.createBy_dictText }}</span>
              <span>创建时间: {{ item.createTime }}</span>
            </div>
            <div class="flex gap-x-[40px] mt-[12px]" v-else-if="item.name === '中标'">
              <span>操作人: {{ item.operator_dictText }}</span>
              <span>业务时间: {{ item.bizDate }}</span>
              <span>中标结果: {{ item.customer_dictText }}</span>
            </div>
            <div class="flex gap-x-[40px] mt-[12px]" v-else-if="item.name === '流标'">
              <span>操作人: {{ item.operator_dictText }}</span>
              <span>业务时间: {{ item.bizDate }}</span>
              <span>流标说明: {{ item.remark }}</span>
            </div>
            <div class="flex gap-x-[40px] mt-[12px]" v-else>
              <span>操作人: {{ item.createBy_dictText }}</span>
              <span>变更时间: {{ item.createTime }}</span>
            </div>
            <file-list :biz-id="item.id" :show-empty="false" class="mt-[12px]"></file-list>
          </li>
        </ul>
        <a-empty description="当前招租方案暂无跟踪记录" v-show="!(record.logList && record.logList.length)">
          <template #image>
            <img src="@/assets/imgs/no-data.png" class="m-auto" />
          </template>
        </a-empty>
      </template>
    </a-table>
    <edit-rent-scheme ref="editRentSchemeRef" @refresh="refresh"></edit-rent-scheme>
    <rent-scheme-detail
      ref="rentSchemeDetailRef"
      :data-list="list"
      @edit-rent-scheme="handleEdit"
      @refresh="refreshFromDetail"
    ></rent-scheme-detail>
    <set-result ref="setResultRef" @refresh="refresh"></set-result>
    <common-import
      ref="commonImportRef"
      :download-fn="() => exportExcel('招租方案导入模板.xls', { id: 0 })"
      :upload-fn="importExcel"
      @refresh="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
    ></common-import>
    <customer-detail ref="customerDetailRef" :data-list="[]"></customer-detail>
    <contract-detail ref="contractDetailRef" :data-list="[]"></contract-detail>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import { page, deleteBatch, exportExcel, importExcel, audit, unAudit, logList, withdraw } from './apis.js'
import EditRentScheme from './components/EditRentScheme.vue'
import RentSchemeDetail from './components/RentSchemeDetail.vue'
import SetResult from './components/SetResult.vue'
import { Modal, message } from 'ant-design-vue'
import { renderDictTag } from '@/utils/render'
import CustomerDetail from '@/views/customer/manage/components/CustomerDetail.vue'
import ContractDetail from '@/views/contract/management/components/ContractDetail.vue'

const route = useRoute()
const router = useRouter()

const pageTitle = computed(() => route.meta.title)

const params = reactive({
  id: undefined,
  number: undefined,
  title: undefined,
  status: undefined,
  bizStatus: undefined,
  manageCompany: undefined,
  bizDate: undefined,
  operator: undefined,
  operatorDepart: undefined,
  auditDocumentNo: undefined,
  reviewDocumentNo: undefined,
  publicStartTime: undefined,
  publicEndTime: undefined,
  rentType: undefined,
  publicDate: undefined,
  bearerObject: undefined,
  totalArea: undefined,
  referencePrice: undefined,
  limitPrice: undefined,
  price: undefined,
  rentMonths: undefined,
  priceIncrease: undefined,
  managerange: undefined,
  environmental: undefined,
  supporting: undefined,
  advantage: undefined,
  redecorateReq: undefined,
  otherReq: undefined,
  remark: undefined,
  createBy: undefined,
  createTime: undefined,
  updateBy: undefined,
  updateTime: undefined
})
const searchList = [
  { label: '业务日期', name: 'bizDate', type: 'date' },
  { label: '单据状态', name: 'status', type: 'dict-select', code: 'CT_BASE_ENUM_AuditStatus' },
  { label: '操作人', name: 'operator', type: 'user-select' },
  { label: '招租总面积', name: 'totalArea', type: 'input' },
  { label: '单位租金', name: 'price', type: 'input' },
  { label: '经营范围', name: 'managerange', type: 'input' },
  { label: '租赁期限', name: 'rentMonths', type: 'input' },
  { label: '业务状态', name: 'bizStatus', type: 'dict-select', code: 'CT_BASE_ENUM_RentScheme_BizStatus' }
]

const defaultColumns = [
  { title: '招租方案名称', dataIndex: 'title', width: 200, fixed: 'left' },
  { title: '业务时间', dataIndex: 'bizDate', width: 120 },
  {
    title: '单据状态',
    dataIndex: 'status',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_AuditStatus', 'dot')
  },
  { title: '操作人', dataIndex: 'operator_dictText', width: 100 },
  { title: '招租总面积', dataIndex: 'totalArea', width: 120, customRender: ({ text }) => `${text}㎡` },
  { title: '单位租金', dataIndex: 'price', width: 120, customRender: ({ text }) => `${text}元/㎡` },
  { title: '经营范围', dataIndex: 'managerange', width: 140 },
  { title: '租赁期限', dataIndex: 'rentMonths', width: 100, customRender: ({ text }) => `${text}个月` },
  {
    title: '业务状态',
    dataIndex: 'bizStatus',
    width: 100,
    customRender: ({ text }) => renderDictTag(text, 'CT_BASE_ENUM_RentScheme_BizStatus', 'dot')
  },
  { title: '中标结果', dataIndex: 'bidResult_dictText', width: 100 },
  { title: '签约合同', dataIndex: 'contract', width: 100 },
  { title: '方案编码', dataIndex: 'number', width: 180 },
  { title: '流标次数', dataIndex: 'failedBiddingCount', width: 100 },
  { title: '变更次数', dataIndex: 'chanageCount', width: 100 },
  { title: '操作', dataIndex: 'action', width: 120, fixed: 'right' }
]
const columnSetRef = ref()
const columns = computed(() => columnSetRef.value?.columns)

const { list, pagination, tableLoading, onTableFetch, tableHeight } = usePageTable(page)
const onTableChange = async ({ current, pageNo = 1, pageSize = 10 } = {}) => {
  await onTableFetch({ pageNo: current ?? pageNo, pageSize, ...params })
  list.value.forEach((item) => {
    if (expandedRowKeys.value.includes(item.id)) {
      loadLogList(item)
    }
  })
}

const expandedRowKeys = ref([])

let timer
const handleInput = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

const editRentSchemeRef = ref()
const handleAdd = () => {
  editRentSchemeRef.value.open()
}
const handleEdit = (data) => {
  editRentSchemeRef.value.open(data.id)
}

const rentSchemeDetailRef = ref()
const handleView = (data) => {
  logList({ parent: data.id })
  rentSchemeDetailRef.value.open(data.id)
}

const handleWithdraw = (data) => {
  Modal.confirm({
    title: '是否确认撤回该招租方案？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await withdraw({ id: data.id })
      message.success('已撤回')
      onTableChange(pagination.value)
    }
  })
}

const customerDetailRef = ref()
const handleViewCustomer = (data) => {
  customerDetailRef.value.open({ id: data.bidResult })
}

const contractDetailRef = ref()
const handleViewContract = (data) => {
  contractDetailRef.value.open(data.contract)
}

const handleAudit = async (record, result) => {
  result ? await audit({ id: record.id }) : await unAudit({ id: record.id })
  onTableChange(pagination.value)
}

const handleExpand = (expand, record) => {
  if (!expand) return
  loadLogList(record)
}

// 获取招租方案跟踪记录
const loadLogList = async (record) => {
  const { result } = await logList({ parent: record.id })
  record.logList = result.records
}

const setResultRef = ref()
const handleSetResult = (record) => {
  setResultRef.value.open(record.id)
}

const handleCreateContract = (data) => {
  sessionStorage.setItem('rentSchemeInfo', JSON.stringify({ id: data.id, customer: data.bidResult }))
  router.push({ path: '/contract/management' })
}

/**
 * 删除
 * @param {Boolean | Object} data 当data为false时，表示批量删除
 */
const handleRemove = (data) => {
  Modal.confirm({
    title: '确认删除招租方案？',
    content: undefined,
    centered: true,
    onOk: async () => {
      await deleteBatch({ ids: data.id })
      message.success('删除成功')
      let pageNo = pagination.value.current
      if (pageNo > 1 && list.value.length === 1) {
        pageNo--
      }
      onTableChange({ pageNo, pageSize: pagination.value.pageSize })
    }
  })
}

const refresh = (resetPageNo) => {
  onTableChange(resetPageNo ? { pageNo: 1, pageSize: pagination.value.pageSize } : pagination.value)
}

// 由详情弹窗的操作，引发的列表数据更新
const refreshFromDetail = (isDelete) => {
  if (isDelete) {
    let pageNo = pagination.value.current
    if (pageNo > 1 && list.value.length === 1) {
      pageNo--
    }
    onTableChange({ pageNo, pageSize: pagination.value.pageSize })
  } else {
    onTableChange(pagination.value)
  }
}

const commonImportRef = ref()
const handleImport = () => {
  commonImportRef.value.open()
}

const exportLoading = ref(false)
const handleExport = async () => {
  if (exportLoading.value) return
  try {
    exportLoading.value = true
    await exportExcel('招租方案数据导出.xls', params)
    message.success('导出成功')
  } finally {
    exportLoading.value = false
  }
}

onMounted(() => {
  onTableChange()
})
</script>

<style lang="less" scoped>
.log-li {
  padding-left: 16px;
  position: relative;
  padding-bottom: 10px;
  &:last-child {
    padding-bottom: 0;
    &::after {
      content: none;
    }
  }
  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--color-primary);
    position: absolute;
    left: 0;
    top: 8px;
    z-index: 1;
  }
  &::after {
    content: '';
    position: absolute;
    left: 4px;
    top: 8px;
    width: 1px;
    height: 100%;
    background-color: #e0e0e0;
  }
}
:deep(.ant-table-wrapper) {
  tr.ant-table-expanded-row > td {
    background-color: #fff;
  }
}
</style>

import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'
export const getPage = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/houseDealBill/list',
    params
  })
}
// 提交
export const submit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/houseDealBill/submit',
    data
  })
}
// 添加
export const stash = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/houseDealBill/add',
    data
  })
}
// 编辑
export const edit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/houseDealBill/edit',
    data
  })
}
// 详情 通过id
export const detailById = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/houseDealBill/queryById?id=${id}`
  })
}

// 通过id删除
export const delById = (id) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/houseDealBill/delete?id=${id}`
  })
}
// 批量删除
export const deleteBatch = (ids) => {
  return request({
    method: 'delete',
    url: `/biz/basicdatadeal/houseDealBill/deleteBatch?ids=${ids}`
  })
}
// 反审核
export const unAudit = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/houseDealBill/unAudit',
    data
  })
}
// 撤回
export const back = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/houseDealBill/back',
    data
  })
}

// 资产处置单-分录主表ID查询
export const queryHouseDealBillEntryByMainId = (id) => {
  return request({
    method: 'get',
    url: `/biz/basicdatadeal/houseDealBill/queryHouseDealBillEntryByMainId?id=${id}`
  })
}

// 导出
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/houseDealBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}
// 导入
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/houseDealBill/importExcel', data, controller)
}

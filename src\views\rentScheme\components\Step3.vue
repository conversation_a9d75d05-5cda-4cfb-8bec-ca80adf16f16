<template>
  <a-form
    :model="form"
    ref="formRef"
    :rules="rules"
    :label-col="{ style: { width: '114px' } }"
    label-align="left"
    autocomplete="off"
  >
    <a-form-item label="承担对象要求" name="bearerObject">
      <dict-select v-model="form.bearerObject" code="CT_BASE_ENUM_RentScheme_Rentobj"></dict-select>
    </a-form-item>
    <a-form-item label="招租总面积(m²)" name="totalArea">
      <a-input v-model:value="form.totalArea" :maxlength="10" disabled>
        <template #suffix>m²</template>
      </a-input>
    </a-form-item>
    <a-form-item label="市场参考价" name="referencePrice">
      <a-input v-model:value="form.referencePrice" :maxlength="10">
        <template #suffix>元</template>
      </a-input>
    </a-form-item>
    <a-form-item label="招租底价" name="limitPrice">
      <a-input v-model:value="form.limitPrice" :maxlength="10" @blur="handleLimitPriceChange">
        <template #suffix>元</template>
      </a-input>
    </a-form-item>
    <a-form-item label="单位租金" name="price">
      <a-input v-model:value="form.price" :maxlength="10" disabled>
        <template #suffix>元/m²/月</template>
      </a-input>
    </a-form-item>
    <a-form-item label="租赁期限(月)" name="rentMonths">
      <a-input v-model:value="form.rentMonths" :maxlength="4">
        <template #suffix>月</template>
      </a-input>
    </a-form-item>
    <a-form-item label="价格递增方式" name="priceIncrease">
      <dict-select
        v-model="form.priceIncrease"
        code="CT_BASE_ENUM_ContractLeaseFundsPriceIncreaseWay_YearMonthPeriodWay"
      ></dict-select>
    </a-form-item>
    <a-form-item label="押付方式" name="presspayWay">
      <dict-select v-model="form.presspayWay" code="ct_bas_presspay_way"></dict-select>
    </a-form-item>
    <a-form-item label="经营范围" name="managerange">
      <a-input v-model:value="form.managerange" :maxlength="100"></a-input>
    </a-form-item>
    <a-form-item label="环保条件" name="environmental">
      <a-input v-model:value="form.environmental" :maxlength="100"></a-input>
    </a-form-item>
    <a-form-item label="配套情况" name="supporting">
      <a-input v-model:value="form.supporting" :maxlength="100"></a-input>
    </a-form-item>
    <a-form-item label="区域优势" name="advantage">
      <a-input v-model:value="form.advantage" :maxlength="100"></a-input>
    </a-form-item>
    <a-form-item label="对装修期要求" name="redecorateReq">
      <a-input v-model:value="form.redecorateReq" :maxlength="100"></a-input>
    </a-form-item>
    <a-form-item label="其他规范要求" name="otherReq" class="form-item-full">
      <a-textarea
        v-model:value="form.otherReq"
        show-count
        :maxlength="500"
        :auto-size="{ minRows: 5, maxRows: 5 }"
      ></a-textarea>
    </a-form-item>
  </a-form>
</template>

<script setup>
import { validateMoney, oneDecimalRegexp } from '@/utils/validate'
import Decimal from 'decimal.js'

const { form } = defineProps({
  form: { type: Object, required: true }
})

const validateMonth = (_, value) => {
  value = String(value)
  if (!value) return Promise.reject('请输入租赁期限')
  if (!oneDecimalRegexp.test(value)) {
    return Promise.reject(value.includes('.') ? '租赁期限不合法，小数点后最多保留一位小数' : '请填写正确的租赁期限')
  }
  return Promise.resolve()
}

const rules = {
  bearerObject: [{ required: true, message: '请选择承担对象要求', trigger: 'change' }],
  totalArea: [{ required: true, message: '招标总面积', trigger: 'blur' }],
  referencePrice: [{ required: false, validator: validateMoney(false, '市场参考价'), trigger: 'blur' }],
  limitPrice: [{ required: true, validator: validateMoney(true, '招租底价'), trigger: 'blur' }],
  rentMonths: [{ required: true, validator: validateMonth, trigger: 'blur' }],
  paymentMethod: [{ required: true, message: '请选择押付方式', trigger: 'blur' }]
}

const handleLimitPriceChange = () => {
  form.price = new Decimal(Number(form.limitPrice)).div(new Decimal(Number(form.totalArea))).toFixed(2)
}

const formRef = ref()

const validate = async () => {
  await formRef.value.validate()
}

const validateFields = async () => {
  const fields = []
  for (const key in rules) {
    if (form[key]) {
      fields.push(key)
    }
  }
  await formRef.value.validateFields(fields)
}

const clearValidate = () => {
  formRef.value.clearValidate()
}

defineExpose({ validate, clearValidate, validateFields })
</script>

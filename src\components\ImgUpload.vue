<template>
  <div
    class="img-upload"
    :class="{ disabled, isRound: round }"
    :style="{ width: size, height: size }"
    @click="handleInputClick"
  >
    <i :class="loading ? 'a-icon-loading' : 'a-icon-plus'" class="text-[24px] text-[#999]"></i>
    <input
      type="file"
      v-show="false"
      accept="image/jpg, image/png, image/jpeg, image/bmp, image/gif"
      ref="inputFileRef"
      @change="onchange"
    />
    <div v-if="modelValue" class="w-full h-full rounded-[8px] absolute left-[0] top-[0] z-[10] overflow-hidden">
      <a-image :src="imgUrl" :width="120" @click.stop></a-image>
    </div>
    <div
      class="absolute w-[28px] h-[28px] rounded-full right-[0] top-[0] z-[11] bg-[rgba(0,0,0,0.6)] flex items-center justify-center cursor-pointer"
      v-if="modelValue && !disabled"
      @click.stop="handleRemove"
    >
      <i class="a-icon-close text-white"></i>
    </div>
  </div>
</template>

<script setup>
import { uploadFile, getFileAccessHttpUrl } from '@/apis/common'
import { message } from 'ant-design-vue'

const props = defineProps({
  size: { type: String, default: '120px' },
  fileSize: { type: Number, default: 2 }, // 单位MB
  disabled: { type: Boolean, default: false },
  round: { type: Boolean, default: false },
  modelValue: { type: String, default: '' } // 图片地址
})

const emit = defineEmits(['update:modelValue'])

const imageTypes = ['image/jpg', 'image/png', 'image/jpeg', 'image/bmp', 'image/gif']

const inputFileRef = ref()
const handleInputClick = () => {
  if (props.disabled) return
  inputFileRef.value.click()
}

const loading = ref(false)
const onchange = async (e) => {
  const files = e.target.files
  if (!(files && files.length)) return
  const file = files[0]
  if (!imageTypes.includes(file.type)) {
    message.warning('仅支持上传jpg, jpeg, png, bmp, gif格式的图片')
    return
  }
  if (file.size / 1024 / 1024 > props.size) {
    message.warning(`上传图片大小不得超过${props.size}Mb`)
    return
  }
  try {
    loading.value = true
    const formData = new FormData()
    formData.append('file', file)
    formData.append('biz', 'temp')
    e.target.value = '' // 清空已选图片
    const { message } = await uploadFile(formData)
    emit('update:modelValue', message)
    loading.value = false
  } catch {
    loading.value = false
  }
}

const imgUrl = computed(() => getFileAccessHttpUrl(props.modelValue))

const handleRemove = () => {
  emit('update:modelValue', '')
}
</script>

<style lang="less" scoped>
.img-upload {
  border: 1px dashed #e0e0e0;
  border-radius: 8px;
  position: relative;
  cursor: pointer;
  transition: border-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  &.isRound {
    border-radius: 50%;
    img {
      border-radius: 50%;
    }
  }
  &:hover {
    border-color: var(--color-primary);
    & > i {
      transition: color 0.2s;
      color: var(--color-primary);
    }
  }
}
</style>

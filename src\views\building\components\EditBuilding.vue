<template>
  <a-drawer
    v-model:open="visible"
    class="edit-building-drawer common-drawer"
    :title="form.id ? '编辑楼栋' : '新建楼栋'"
    placement="right"
    width="1072px"
    :mask-closable="false"
    @close="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form
        :model="form"
        ref="formRef"
        :rules="rules"
        :label-col="{ style: { width: '72px' } }"
        label-align="left"
        autocomplete="off"
      >
        <h2 class="text-[16px] font-bold mb-[20px]">楼栋基础信息</h2>
        <div class="flex gap-[40px]">
          <a-form-item label="楼栋名称" name="name" class="flex-1">
            <a-input v-model:value="form.name" placeholder="请输入楼栋名称" :maxlength="20"></a-input>
          </a-form-item>
          <a-form-item label="所属项目" name="wyProject" class="flex-1">
            <api-select
              v-model="form.wyProject"
              :async-fn="getProjectList"
              :field-names="{ label: 'name', value: 'id' }"
              placeholder="请选择所属项目"
              :disabled="Boolean(projectId)"
            ></api-select>
          </a-form-item>
        </div>
        <a-form-item label="备注" name="remark">
          <a-textarea
            v-model:value="form.remark"
            placeholder="请输入备注(选填)"
            show-count
            :maxlength="500"
            :auto-size="{ minRows: 4, maxRows: 4 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
      <h2 class="text-[16px] font-bold mb-[20px]">楼层</h2>
      <section class="max-h-[calc(100vh-470px)] overflow-y-auto">
        <draggable v-model="form.wyFloorList" handle=".a-icon-move" item-key="id">
          <template #item="{ element, index }">
            <div class="flex items-center justify-between mb-[16px] last-of-type:mb-[0]">
              <i
                class="a-icon-move cursor-move text-tertiary mr-[8px] text-[20px]"
                v-show="form.wyFloorList.length > 1"
              ></i>
              <a-popconfirm title="是否确认移除？" @confirm="handleRemoveFloor(index)">
                <div>
                  <i class="a-icon-remove cursor-pointer text-tertiary mr-[12px] text-[20px] hover:text-error"></i>
                </div>
              </a-popconfirm>
              <a-input v-model:value="element.name" placeholder="请输入楼层名称" :maxlength="50" show-count></a-input>
              <span class="primary-btn ml-[12px] shrink-0 w-[190px]" @click="handleSetFloorWaterElectricity(element)">
                <i class="a-icon-setting"></i>
                设置楼层水电分摊信息
                <span v-if="element.waterShareFormulas.length">({{ element.waterShareFormulas.length }})</span>
              </span>
            </div>
          </template>
        </draggable>
        <a-button type="primary" ghost size="medium" class="mt-[20px] add-floor-btn" @click="handleAddFloor">
          <i class="a-icon-plus"></i>
          添加楼层
        </a-button>
      </section>
      <div class="flex items-center justify-between mt-[40px] mb-[16px]">
        <h2 class="text-[16px] font-bold">楼栋水电分摊信息</h2>
        <div class="flex items-center">
          <span>自动出账时间: 每月</span>
          <a-input
            v-model:value="form.everyMonthAutoBillDay"
            class="!w-[60px] !mx-[10px]"
            size="medium"
            :maxlength="2"
          ></a-input>
          <span>日</span>
        </div>
      </div>
      <set-info :list="form.waterShareFormulas"></set-info>
      <set-info-modal
        ref="setInfoModalRef"
        @updateFloorWaterShareFormulas="updateFloorWaterShareFormulas"
      ></set-info-modal>
    </a-spin>
    <template #footer>
      <a-button type="primary" :loading="confirmLoading" @click="handleSubmit(false)">提交</a-button>
      <a-button type="primary" ghost :loading="confirmLoading" @click="handleSubmit(true)">提交并继续添加</a-button>
      <a-button @click="handleCancel">取消</a-button>
    </template>
  </a-drawer>
</template>

<script setup>
import { addBuilding, editBuilding, buildingDetail, queryFloor, verifyName } from '../apis/building.js'
import { projectPage } from '@/views/projects/apis.js'
import draggable from 'vuedraggable'
import { message } from 'ant-design-vue'
import SetInfo from './SetInfo.vue'
import setInfoModal from './SetInfoModal.vue'

const { projectId } = defineProps({
  projectId: { type: String, default: '' }
})

const emit = defineEmits(['refresh'])

const visible = ref(false)

const getProjectList = () => projectPage({ pageNo: 1, pageSize: 100000, status: 'ENABLE' })

const open = (id) => {
  form.wyProject = projectId || ''
  visible.value = true
  if (id) {
    loadDetail(id)
  }
}

const loading = ref(false)
const loadDetail = async (id) => {
  loading.value = true
  const { result } = await buildingDetail({ id })
  form.id = result.id
  form.wyProject = result.wyProject
  form.name = result.name
  form.remark = result.remark
  form.status = result.status
  const { result: wyFloorList } = await queryFloor({ id: result.id })
  form.wyFloorList = wyFloorList.map((item) => {
    item.waterShareFormulas = item.waterShareFormulas || []
    return item
  })
  loading.value = false
}

const form = reactive({
  id: '',
  wyProject: '',
  name: '',
  remark: '',
  status: 'ENABLE',
  everyMonthAutoBillDay: '',
  wyFloorList: [],
  waterShareFormulas: []
})

const validateName = async (_, value) => {
  if (!value) return Promise.reject('请输入楼栋名称')
  if (!form.wyProject) return Promise.resolve()
  const { result } = await verifyName({
    name: value,
    wyProject: form.wyProject,
    id: form.id || undefined
  })
  if (result) return Promise.reject('项目中，已存在该楼栋名称')
  return Promise.resolve()
}
const rules = {
  name: [{ required: true, validator: validateName, trigger: 'blur' }],
  wyProject: [{ required: true, message: '请选择所属项目', trigger: 'change' }]
}

const handleAddFloor = async () => {
  form.wyFloorList.push({
    id: Math.random().toString(),
    name: '',
    status: 'ENABLE',
    everyMonthAutoBillDay: '',
    waterShareFormulas: []
  })
  await nextTick()
  document.querySelector('.edit-building-drawer .add-floor-btn').scrollIntoView({ behavior: 'smooth' })
}

const handleRemoveFloor = (index) => {
  form.wyFloorList.splice(index, 1)
}

const setInfoModalRef = ref()
// 设置楼层水电分摊信息
const handleSetFloorWaterElectricity = (data) => {
  if (data.name.trim() === '') {
    data.name = ''
    message.warning('请先输入楼层名称')
    return
  }
  setInfoModalRef.value.open({
    id: data.id,
    everyMonthAutoBillDay: data.everyMonthAutoBillDay,
    waterShareFormulas: data.waterShareFormulas
  })
}

const updateFloorWaterShareFormulas = ({ id, waterShareFormulas, everyMonthAutoBillDay }) => {
  const data = form.wyFloorList.find((i) => i.id === id)
  data.everyMonthAutoBillDay = everyMonthAutoBillDay
  data.waterShareFormulas = waterShareFormulas
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async (isContinue) => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  const result = form.wyFloorList.some((item, index) => {
    if (item.name.trim() === '') {
      message.warning(`第${index + 1}行楼层名称不可为空`)
      return true
    }
    return false
  })
  if (result) return
  try {
    confirmLoading.value = true
    const params = JSON.parse(JSON.stringify(form))
    params.wyFloorList = params.wyFloorList.map((item) => {
      if (item.id.includes('.')) {
        item.id = ''
      }
      return item
    })
    form.id ? await editBuilding(params) : await addBuilding(params)
    confirmLoading.value = false
    isContinue ? resetForm() : handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  if (!projectId) {
    form.wyProject = ''
  }
  form.name = ''
  form.remark = ''
  form.status = 'ENABLE'
  form.wyFloorList = []
  form.waterShareFormulas = []
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>

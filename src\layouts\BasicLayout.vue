<template>
  <top-header></top-header>
  <main class="flex">
    <aside
      class="h-[calc(100vh-64px)] pt-[32px] border-r border-solid border-[#d7dae0]"
      :class="{ 'basic-layout-aside': !state.collapsed }"
    >
      <div class="flex items-center justify-center h-[28px] mb-[24px] px-[16px]">
        <h2 class="text-[18px] font-bold flex-1 line-clamp-1 max-w-[222px]" v-show="!state.collapsed">
          {{ currentMenu.title }}
        </h2>
        <i
          class="a-icon-expand text-[20px] cursor-pointer hover:text-primary transition-colors"
          v-if="state.collapsed"
          @click="state.collapsed = false"
        ></i>
        <i
          class="a-icon-pack-up text-[20px] cursor-pointer hover:text-primary transition-colors"
          v-else
          @click="state.collapsed = true"
        ></i>
      </div>
      <a-menu
        v-model:open-keys="state.openKeys"
        v-model:selected-keys="state.selectedKeys"
        mode="inline"
        :inline-collapsed="state.collapsed"
        :items="menus"
        @click="handleMenuClick"
        class="aside-menu"
      ></a-menu>
    </aside>
    <section class="flex-1 h-[calc(100vh-64px)] overflow-hidden bg-[#f7f8fa] p-[16px]">
      <router-view
        id="basic-router-view"
        class="bg-white rounded-[8px] p-[16px] h-full overflow-auto scrollbar"
      ></router-view>
    </section>
  </main>
</template>

<script setup>
import TopHeader from './components/Header.vue'
import { useUserStore } from '@/store/modules/user'
const store = useUserStore()
const router = useRouter()
const route = useRoute()
const currentMenu = computed(() => store.currentMenu)
const menus = computed(() => store.currentMenu.list.map(getMenuItem))
const getMenuItem = (item) => {
  return {
    key: item.id,
    icon: () => h('i', { class: item.meta.icon }),
    label: item.meta.title,
    title: item.meta.title,
    path: item.path,
    children: item.children ? item.children.map(getMenuItem) : undefined
  }
}
const state = reactive({
  collapsed: false,
  selectedKeys: [],
  openKeys: []
})
const handleMenuClick = ({ item }) => {
  router.push(item.path)
}

/**
 * 根据当前路由地址，获取其在菜单树中的访问路径
 * @param {Array} list 菜单树
 * @param {String} path 当前路由
 * @return {String} 访问路径，即祖级id/父级id/子级id
 */
const getMenuIdByPath = (list, path) => {
  for (let i = 0; i < list.length; i++) {
    if (list[i].path === path) return list[i].key
    if (list[i].children) {
      const childPath = getMenuIdByPath(list[i].children, path)
      if (childPath) return `${list[i].key}/${childPath}`
    }
  }
}

watchEffect(() => {
  const path = getMenuIdByPath(menus.value, route.path)
  if (!path) {
    state.selectedKeys = []
    state.openKeys = []
    return
  }
  const list = path.split('/')
  state.selectedKeys = [list[list.length - 1]]
  state.openKeys = list.slice(0, list.length - 1)
})
</script>

<style lang="less" scoped>
:deep(.aside-menu) {
  .ant-menu-item-icon {
    font-size: 18px !important;
  }
}
.basic-layout-aside {
  :deep(.aside-menu) {
    width: 270px;
    height: calc(100% - 52px);
    border-inline-end: none;
    overflow-y: auto;
    .no-scrollbar();
  }
  :deep(.ant-menu-title-content) {
    font-size: 16px;
  }
  :deep(.ant-menu-sub) {
    border-left: 1px solid #d7dae0;
    margin-left: 32px;
    background: #fff !important;
    .ant-menu-item {
      padding: 0 !important;
      margin-inline: 0;
      margin-block: 0;
      margin-left: 12px;
      width: calc(100% - 28px);
      line-height: 48px;
      height: 48px;
      &:hover {
        background-color: transparent;
        color: var(--color-primary);
      }
      &-selected {
        background-color: var(--color-primary) !important;
        color: #fff !important;
      }
    }
    .ant-menu-title-content {
      margin-left: 20px;
    }
    i {
      display: none;
    }
  }
}
</style>

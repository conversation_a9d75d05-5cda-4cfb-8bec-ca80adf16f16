<template>
  <a-drawer v-model:open="visible" class="common-detail-drawer" width="1072px">
    <template #extra>
      <div class="flex items-center justify-between">
        <div class="flex">
          <span
            class="mr-[16px] text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === 0 || dataList.length === 0 }"
            @click="handleSwitchDetail(currentIndex - 1)"
          >
            <i class="a-icon-arrow-up"></i>
            上一条
          </span>
          <span
            class="text-primary cursor-pointer"
            :class="{ '!cursor-not-allowed !text-primary/60': currentIndex === dataList.length - 1 }"
            @click="handleSwitchDetail(currentIndex + 1)"
          >
            <i class="a-icon-arrow-down"></i>
            下一条
          </span>
        </div>
        <div class="flex border-0 border-r border-solid border-[#E6E9F0] pr-[16px]">
          <span class="text-primary cursor-pointer mr-[16px]" @click="handleEdit">编辑</span>
        </div>
      </div>
    </template>

    <a-spin :spinning="loading">
      <div class="flex items-center mb-[12px]">
        <h2 class="text-[18px] font-bold mr-[12px] text-[#1d335c]">{{ detailData.templateName }}</h2>
      </div>
      <div class="flex items-center h-[32px] bg-[#f7f8fa] rounded-[8px] px-[8px] text-tertiary mb-[40px]">
        <span>{{ detailData.templateCode || '-' }}</span>
        <span class="w-[1px] bg-[#D7DAE0] h-[16px] mx-[16px]"></span>
        <span class="">{{ detailData.createBy || '-' }} 提交于 {{ detailData.createTime || '-' }}</span>
      </div>
      <div id="basic" class="mb-[40px]">
        <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">基础信息</h2>
        <div class="flex flex-wrap gap-y-[12px] text-secondary">
          <span class="w-[50%]">模板标题：{{ detailData.templateName || '-' }}</span>
          <span class="w-[50%]">模板类型：{{ detailData.templateType }}</span>
          <span class="w-[100%]">是否启用：{{ detailData.isEnabled }}</span>
        </div>
      </div>
      <h2 class="text-[16px] font-bold mb-[12px] text-[#1d335c]">模版内容</h2>
      <a-typography-paragraph>
        <div class="p-[20px] bg-[#F5F5F5] rounded-[8px]" v-html="detailData.templateContent"></div>
      </a-typography-paragraph>
    </a-spin>
  </a-drawer>
  <edit-template ref="editTemplateRef" @refresh="loadDetail(detailData.id)" />
</template>

<script setup>
import { getTemplateById } from '../apis'
import EditTemplate from './EditTemplate.vue'

const { dataList } = defineProps({
  dataList: { type: Array, required: true }
})

const editTemplateRef = ref()

const visible = ref(false)
const loading = ref(false)
const detailData = ref({})

const currentIndex = computed(() => {
  if (!detailData.value.id) return 0
  return dataList.findIndex((i) => i.id === detailData.value.id)
})

/**
 * 打开详情抽屉
 */
const open = async (id) => {
  visible.value = true
  await loadDetail(id)
}

/**
 * 切换详情
 */
const handleSwitchDetail = (index) => {
  if (index < 0 || index >= dataList.length) return
  const item = dataList[index]
  if (item) {
    loadDetail(item.id)
  }
}

/**
 * 编辑当前模板
 */
const handleEdit = () => {
  editTemplateRef.value.open(detailData.value)
}

/**
 * 加载模板详情
 */
const loadDetail = async (id) => {
  loading.value = true
  try {
    const response = await getTemplateById({ id })
    const data = response.result || response.data || response
    detailData.value = data
  } finally {
    loading.value = false
  }
}

defineExpose({ open })
</script>

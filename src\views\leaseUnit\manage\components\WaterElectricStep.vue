<template>
  <div>
    <!-- 水电表格 -->
    <div class="mb-4 flex justify-between items-center">
      <h4 class="text-[16px] font-bold my-[24px] text-[#1d335c]">自用水电表</h4>
      <a-button type="primary" @click="handleAdd">
        <i class="a-icon-plus mr-1"></i>
        添加
      </a-button>
    </div>
    <a-table
      :columns="waterElectricColumns"
      :data-source="formData.waterShareFormulas"
      :scroll="{ x: 2000 }"
      :pagination="false"
      row-key="id"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <a-space>
            <a @click="handleEdit">编辑</a>
            <a @click="handleDelete" class="text-red-500">删除</a>
          </a-space>
        </template>
        <template v-if="column.formulaField">
          <a @click="handleSelectFormula">
            {{ record[column.dataIndex] || '点击选择' }}
          </a>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
defineProps({
  formData: {
    type: Object,
    required: true
  }
})

const waterElectricColumns = [
  { title: '名称', dataIndex: 'name', width: 200, fixed: 'left' },
  { title: '编码(表号)', dataIndex: 'waterEleTableNum', width: 150 },
  { title: '类型', dataIndex: 'type', width: 100 },
  { title: '属性', dataIndex: 'property', width: 100 },
  { title: '倍率', dataIndex: 'ratio', width: 100 },
  { title: '单价', dataIndex: 'price', width: 100 },
  { title: '损耗量计算公式', dataIndex: 'lossFormula', width: 180, formulaField: true },
  { title: '单位分摊计算公式', dataIndex: 'unitShareFormula', width: 180, formulaField: true },
  { title: '公摊金额计算公式', dataIndex: 'publicShareFormula', width: 180, formulaField: true },
  { title: '减免金额计算公式', dataIndex: 'reductionFormula', width: 180, formulaField: true },
  { title: '自用金额计算公式', dataIndex: 'selfUseFormula', width: 180, formulaField: true },
  { title: '不含税合计计算公式', dataIndex: 'noTaxTotalFormula', width: 180, formulaField: true },
  { title: '税金计算公式', dataIndex: 'taxFormula', width: 180, formulaField: true },
  { title: '含税合计计算公式', dataIndex: 'taxTotalFormula', width: 180, formulaField: true }
]

/**
 * 添加水电表信息
 */
const handleAdd = () => {}

/**
 * 编辑水电表信息
 */
const handleEdit = () => {}

/**
 * 删除水电表信息
 */
const handleDelete = () => {}

/**
 * 选择计算公式
 */
const handleSelectFormula = () => {}
</script>

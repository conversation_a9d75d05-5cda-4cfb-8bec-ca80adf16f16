<template>
  <h4 class="text-[16px] font-bold mb-[16px]">合同基础信息</h4>
  <div class="flex flex-wrap gap-y-[12px] text-secondary">
    <span class="w-[50%]">合同编码: {{ detail.number }}</span>
    <span class="w-[50%]">签约日期: {{ detail.signDate }}</span>
    <span class="w-[50%]">签约客户: {{ detail.customer_dictText }}</span>
    <span class="w-[50%]">合同类型: {{ detail.contractType_dictText }}</span>
    <span class="w-[50%]">物业管理公司: {{ detail.manageCompany_dictText }}</span>
    <span class="w-[50%]">业务部门: {{ detail.operatorDepart_dictText }}</span>
    <span class="w-[50%]">业务员: {{ detail.operator_dictText }}</span>
    <span class="w-[50%]">定价类型: {{ detail.pricedType_dictText }}</span>
    <span class="w-[50%]">合同开始日期: {{ detail.startDate }}</span>
    <span class="w-[50%]">合同结束日期: {{ detail.expireDate }}</span>
  </div>
  <h4 class="text-[16px] font-bold mt-[40px] mb-[16px]">租赁单元</h4>
  <a-table
    :data-source="detail.leaseUnitsList"
    :columns="leaseUnitColumns"
    :pagination="false"
    :scroll="{ x: '1200px' }"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <span class="primary-btn mr-[10px]" @click="viewUnitDetail(record)">单元详情</span>
      </template>
    </template>
  </a-table>
  <h4 class="text-[16px] font-bold mt-[40px] mb-[16px]">合同款项</h4>
  <a-table
    :data-source="detail.paymentList"
    :columns="paymentColumns"
    :pagination="false"
    :scroll="{ x: 1200 }"
  ></a-table>
  <h4 class="text-[16px] font-bold mt-[40px] mb-[16px]">附件</h4>
  <file-list :biz-id="detail.id"></file-list>
  <lease-unit-detail ref="leaseUnitDetailRef" :data-list="detail.leaseUnitsList"></lease-unit-detail>
</template>

<script setup>
import { renderDict } from '@/utils/render'
import LeaseUnitDetail from '@/views/leaseUnit/manage/components/LeaseUnitDetail.vue'

defineProps({
  detail: { required: true, type: Object }
})

const leaseUnitColumns = [
  { title: '租赁单元名称', dataIndex: 'leaseUnit_dictText', width: 120, fixed: 'left' },
  { title: '地址', dataIndex: 'detailAddress', width: 120 },
  { title: '归集公司', dataIndex: 'collectionCompany_dictText', width: 160 },
  { title: '权属公司', dataIndex: 'ownerCompany_dictText' },
  { title: '片区管理员', dataIndex: 'areaManager_dictText' },
  { title: '租赁面积m²', dataIndex: 'leaseArea' },
  { title: '租赁用途', dataIndex: 'leaseUse_dictText' },
  { title: '产权用途', dataIndex: 'propertyUse_dictText' },
  { title: '消防等级', dataIndex: 'firefightingRate_dictText' }
]

const leaseUnitDetailRef = ref()
const viewUnitDetail = (data) => {
  leaseUnitDetailRef.value.open(data)
}

const paymentColumns = [
  { title: '款项', dataIndex: 'paymentType_dictText', width: 100, fixed: 'left' },
  { title: '缴交周期', dataIndex: 'period', width: 90, customRender: ({ text }) => `${text}个月` },
  { title: '金额', dataIndex: 'amountPerPeriod', width: 90, customRender: ({ text }) => `${text}元` },
  { title: '每期金额', dataIndex: 'amountPerMonth', width: 90, customRender: ({ text }) => `${text}元/月` },
  {
    title: '起止日期',
    dataIndex: 'startDate',
    customRender: ({ record }) => `${record.startDate}~${record.expireDate}`
  },
  {
    title: '账单生成规则',
    dataIndex: 'createDetailBill',
    customRender: ({ text }) => renderDict(text, 'CT_BASE_ENUM_Contract_CreateDetailBill')
  },
  { title: '应收日', dataIndex: 'advanceDays', customRender: ({ text }) => `比开始日提前${text}天` },
  { title: '备注', dataIndex: 'remark', ellipsis: true },
  { title: '递增规则', dataIndex: 'priceIncreaseWay_dictText' }
]
</script>

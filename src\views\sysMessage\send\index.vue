<template>
  <div>
    <h2 class="text-[18px] font-bold">{{ pageTitle }}</h2>
    <div class="flex items-center justify-between mt-[24px] mb-[16px]">
      <div class="flex items-center">
        <a-dropdown v-if="selectedRowKeys.length">
          <a-button>
            批量操作
            <i class="a-icon-arrow-down ml-[8px]"></i>
          </a-button>
          <template #overlay>
            <a-menu>
              <a-menu-item key="batchDelete">删除</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
        <a-button @click="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })">
          <i class="a-icon-refresh"></i>
          刷新
        </a-button>
        <s-input
          v-model="searchParams.title"
          placeholder="搜索标题"
          class="ml-[40px] !w-[280px]"
          @input="handleSearch"
        ></s-input>
        <filter-more
          :params="searchParams"
          :search-list="searchList"
          width="320px"
          label-width="100px"
          @query="onTableChange({ pageNo: 1, pageSize: pagination.pageSize })"
        ></filter-more>
      </div>
      <columns-set :default-columns="defaultColumns" ref="columnSetRef"></columns-set>
    </div>
    <a-table
      :data-source="list"
      :columns="columns"
      :loading="tableLoading"
      :pagination="pagination"
      row-key="id"
      :row-selection="{ selectedRowKeys, onChange: onSelectChange }"
      @change="onTableChange"
    >
      <template #bodyCell="{ column }">
        <template v-if="column.dataIndex === 'action'">
          <span class="primary-btn">查看</span>
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="edit">编辑</a-menu-item>
                <a-menu-item key="delete">删除</a-menu-item>
              </a-menu>
            </template>
            <span class="primary-btn">
              更多
              <i class="a-icon-arrow-down"></i>
            </span>
          </a-dropdown>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
import usePageTable from '@/hooks/usePageTable'
import useTableSelection from '@/hooks/useTableSelection'
import { getMessageList } from './apis'

const route = useRoute()

const columnSetRef = ref()

// 搜索参数
const searchParams = reactive({
  title: undefined
})
const searchList = reactive({})
// 表格数据和分页
const { list, pagination, tableLoading, onTableFetch } = usePageTable(getMessageList)
const { selectedRowKeys, onSelectChange } = useTableSelection(list, 'id')

// 默认表格列定义
const defaultColumns = [
  { title: '消息标题', dataIndex: 'title', fixed: 'left', width: 150 },
  { title: '发送内容', dataIndex: 'content', width: 200 },
  { title: '接收人', dataIndex: 'recipient', width: 300 },
  { title: '发送次数', dataIndex: 'count', width: 120 },
  { title: '发送状态', dataIndex: 'status', width: 100 },
  { title: '发送时间', dataIndex: 'sendTime', width: 150 },
  { title: '发送方式', dataIndex: 'sendWay', width: 100 },
  { title: '已读/未读', dataIndex: 'readFlag', width: 150 },
  { title: '操作', dataIndex: 'action', width: 150, fixed: 'right' }
]

const columns = computed(() => columnSetRef.value?.columns || defaultColumns)

const pageTitle = computed(() => route.meta.title)
/**
 * 表格变化处理
 */
const onTableChange = ({ pageNo = 1, pageSize = 10 } = {}) => {
  onTableFetch({ pageNo, pageSize, ...searchParams })
}

/**
 * 搜索输入处理
 */
let timer
const handleSearch = () => {
  clearTimeout(timer)
  timer = setTimeout(() => {
    onTableChange({ pageNo: 1, pageSize: pagination.value.pageSize })
  }, 600)
}

onMounted(() => {
  onTableChange()
})
</script>

import request from '@/apis/http'
import { advanceUpload } from '@/apis/common'

// 获取列表数据
export const getLeaseUnitInfoChangeReqBillList = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitInfoChangeReqBill/list',
    params
  })
}

// 获取详情
export const getLeaseUnitInfoChangeReqBillDetail = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitInfoChangeReqBill/queryById',
    params
  })
}

// 根据变更单ID获取租赁单元
export const getLeaseUnitInfoChangeReqBillById = (params) => {
  return request({
    method: 'get',
    url: '/biz/basicdatadeal/leaseUnitInfoChangeReqBill/queryLeaseUnitInfoChangeReqBillEntryByMainId',
    params
  })
}

// 暂存
export const addLeaseUnitInfoChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitInfoChangeReqBill/add',
    data
  })
}

// 提交
export const submitLeaseUnitInfoChangeReqBill = (data) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitInfoChangeReqBill/submit',
    data
  })
}

// 通过 id 删除
export const deleteLeaseUnitInfoChangeReqBill = (params) => {
  return request({
    method: 'delete',
    url: '/biz/basicdatadeal/leaseUnitInfoChangeReqBill/delete',
    params
  })
}

// 导出 Excel
export const exportExcel = (fileName, params) => {
  return request({
    method: 'post',
    url: '/biz/basicdatadeal/leaseUnitInfoChangeReqBill/exportXls',
    responseType: 'blob',
    params,
    fileName,
    timeout: 60 * 5 * 1000 // 5分钟
  })
}

// 导入 Excel
export const importExcel = (data, controller) => {
  return advanceUpload('/biz/basicdatadeal/leaseUnitInfoChangeReqBill/importExcel', data, controller)
}

<template>
  <a-modal
    v-model:open="visible"
    :title="form.id ? '编辑款项税率信息' : '新建款项税率信息'"
    width="600px"
    class="common-modal"
    :mask-closable="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading || confirmLoading">
      <a-form :model="form" ref="formRef" :rules="rules" :label-col="{ style: { width: '100px' } }" autocomplete="off">
        <a-form-item label="客户类型" name="customerType">
          <dict-select v-model="form.customerType" code="CT_BASE_ENUM_Customer_CustomerType"></dict-select>
        </a-form-item>
        <a-form-item label="房产类型" name="houseType">
          <dict-select v-model="form.houseType" code="CT_BASE_ENUM_HouseOwner_HouseType"></dict-select>
        </a-form-item>
        <a-form-item label="定价类型" name="pricedType">
          <dict-select v-model="form.pricedType" code="CT_BASE_ENUM_Contract_PricedType"></dict-select>
        </a-form-item>
        <a-form-item label="月税率" name="monthRate">
          <a-input-number
            v-model:value="form.monthRate"
            :min="0"
            :max="100"
            addon-after="%"
            placeholder="请输入月税率"
            class="!w-full"
          />
        </a-form-item>
        <a-form-item label="备注" name="remark" class="!w-full">
          <a-textarea
            v-model:value="form.remark"
            placeholder="请输入备注(选填)"
            show-count
            :maxlength="200"
            :auto-size="{ minRows: 5, maxRows: 5 }"
          ></a-textarea>
        </a-form-item>
      </a-form>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { add, edit } from '../apis.js'
import { message } from 'ant-design-vue'

const emit = defineEmits(['refresh'])

const visible = ref(false)

const open = (info) => {
  visible.value = true
  if (info.parent) {
    form.parent = info.parent
  }
  form.id = info.id
  form.customerType = info.customerType
  form.houseType = info.houseType
  form.pricedType = info.pricedType
  form.monthRate = info.monthRate
  form.remark = info.remark
}

const loading = ref(false)

const form = reactive({
  id: '',
  parent: '',
  customerType: '',
  houseType: '',
  pricedType: '',
  monthRate: '',
  remark: ''
})

const rules = {
  customerType: [{ required: true, message: '请选择客户类型', trigger: 'change' }],
  houseType: [{ required: true, message: '请选择房产类型', trigger: 'change' }],
  pricedType: [{ required: true, message: '请选择定价类型', trigger: 'change' }],
  monthRate: [{ required: true, message: '请输入月税率', trigger: 'blur' }]
}

const formRef = ref()
const confirmLoading = ref(false)
const handleSubmit = async () => {
  if (confirmLoading.value) return
  await formRef.value.validate()
  try {
    confirmLoading.value = true
    const params = { ...form }
    form.id ? await edit(params) : await add(params)
    confirmLoading.value = false
    handleCancel()
    message.success('保存成功')
    emit('refresh')
  } catch {
    confirmLoading.value = false
  }
}

const resetForm = () => {
  form.id = ''
  form.parent = ''
  form.customerType = ''
  form.houseType = ''
  form.pricedType = ''
  form.monthRate = ''
  form.remark = ''
}

const handleCancel = () => {
  resetForm()
  formRef.value.clearValidate()
  visible.value = false
}

defineExpose({ open })
</script>
